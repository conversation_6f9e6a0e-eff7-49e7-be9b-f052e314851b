# SonarQube server settings
sonar.host.url=https://sast.beatapps.net

# Project settings
sonar.projectKey=beat-foliosure-ui
sonar.projectVersion=2.33.0

# Source configuration
sonar.sources=src
sonar.exclusions=**/node_modules/**,src/assets/**,**/*.spec.ts,**/*.mock.ts

# Test configuration
sonar.tests=src
sonar.test.inclusions=**/*.spec.ts
sonar.test.exclusions=**/node_modules/**

# Coverage reporting
sonar.javascript.lcov.reportPaths=coverage/beat-foliosure-ui/lcov.info
sonar.typescript.exclusions=**/node_modules/**,**/*.spec.ts
sonar.coverage.exclusions=**/*.mock.ts,**/*.module.ts,**/index.ts
