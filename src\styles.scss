/* You can add global styles to this file, and also import other style files */

@import "./variables";
$kendo-color-primary:#4061C7;
$kendo-grid-sticky-selected-bg: #E8EAF6;
$kendo-grid-sticky-selected-alt-bg: #E8EAF6;
// Must be a solid color
$kendo-grid-sticky-hover-bg: #E8EAF6;
$kendo-grid-sticky-selected-hover-bg: #E8EAF6;
.theme-light {
    @import './assets/dist/kendo/theme-light.scss';
}
.theme-dark {
    @import './assets/dist/kendo/theme-dark.scss';
}
@import "@progress/kendo-theme-default/dist/all.scss";
@import "@progress/kendo-font-icons/dist/index.css";
@import "./assets/dist/css/bootstrap/dist/css/bootstrap.css";
@import "~primeicons/primeicons.css";
@import "./assets/dist/css/font-awesome/css/font-awesome.min.css";
@import "@angular/material/prebuilt-themes/deeppurple-amber.css";
@import '@angular/material/theming';
@import "~primeng/resources/primeng.min.css";
@import "~material-design-icons/iconfont/material-icons.css";
@import "./assets/dist/css/toastr.css";
@import "../projects/ng-neptune/src/neptune.scss";
@import "./assets/dist/css/prime-theme";
@import "./assets/dist/css/chart.css";
@import "./assets/dist/responsive.css";
@import "./assets/dist/css/codemirror.css";
@import "./assets/dist/css/show-hint.css";
@import "./common.scss";
@import '~quill/dist/quill.bubble.css';
@import '~quill/dist/quill.snow.css';
@import "./assets/dist/css/font.scss";

$row-height: 43px;

html,
body {
    margin: 0;
    padding: 0;
    height: 100%;
    overflow: hidden !important;
}

textarea {
    resize: none;
    height: 100px;
    max-height: 100px;
    padding: 0.1rem 0.3rem !important;
}

.pr-12 {
    padding-right: 12px;
}

.pl-12 {
    padding-left: 12px;
}

.pt-12 {
    padding-top: 12px;
}

.pb-12 {
    padding-bottom: 12px;
}

a:hover {
    cursor: pointer !important;
}

.p-widget {
    font-size: inherit;
}

// body::-webkit-scrollbar {
//     width: 5px;
// }
// body::-webkit-scrollbar-track {
//     -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
//     border-radius: 10px;
// }
// body::-webkit-scrollbar-thumb {
//     background-color: $nep-deepskyblue;
//     outline: 1px solid $nep-slate-grey;
//     border-radius: 10px;
// }
.display-inline {
    display: inline-block !important;
}

header,
.sidebar {
    width: 100%;
    background-repeat: no-repeat;
    background-color: $nep-primary;
    position: fixed;
    z-index: 999;
    top: 0;
}

.padd-top {
    padding: 53px;
}

.navbar-brand {
    display: inline-block;
    padding-top: 0.3125rem;
    padding-bottom: 0.3125rem;
    margin-right: 1rem;
    font-size: 2.25rem;
    line-height: inherit;
    white-space: nowrap;
    color: $nep-white-secondary;
}

a.navbar-brand:hover {
    color: $nep-white-secondary;
}

.bg-nav {
    background-color: transparent;
}

.nav-menu {
    border-top: 1px solid #ffffff24;
    width: 100%;
    display: block;
}

.bg-nav .navbar-nav .nav-link {
    color: white;
    font-size: 1rem;
    cursor: pointer;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.25rem 1.5rem;
    clear: both;
    font-weight: 400;
    text-align: inherit;
    white-space: nowrap;
    border: 0;
}

.dropdown-item:hover,
.dropdown-item:focus {
    color: $nep-white-secondary !important;
    text-decoration: none;
    background-color: $gray-800;
}

.navbar-dark .navbar-nav .show>.nav-link,
.navbar-dark .navbar-nav .active>.nav-link,
.navbar-dark .navbar-nav .nav-link.show,
.navbar-dark .navbar-nav .nav-link.active {
    color: #a7e3fd !important;
}

.main-body {
    background: $nep-white-secondary;
}

input[type="checkbox"] {
    width: 1.1em;
    height: 1.1em;
    font-size: 1rem;
    vertical-align: middle;
}

input[type="checkbox"]:hover {
    cursor: pointer;
}

.user-info-logout {
    border-color: transparent;
    width: 24px !important;
    height: 24px !important;
    color: $nep-white !important;
    border-radius: 50% !important;
    @extend .Body-R;
    @extend .background-circle-bg0;
}

.loading-login {
    width: 20px;
    margin: 0px auto;
    padding-top: 11px;
}

.user,
.createfund,
.register,
.main,
.aside,
.fund-list,
.firm,
.portfoliocompany-list,
.addportfoliocompany,
.add-firm {
    width: 100%;
    display: block;
    margin: 0px 0;
}

.card-body {
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 0rem 0;
    margin-bottom: 0.5rem;
}

.card-body>form {
    padding: 0rem 0.3rem 0.7rem 0.3rem;
    margin-top: 1rem;
}

.card-body form form {
    padding: 0;
    margin-bottom: 1rem;
}

.card-body .card-body {
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 0;
}

.p-multiselect-header .p-multiselect-filter-container {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    width: 81%;
}

.card>.card-header-main {
    padding: 0.3rem 0.3rem;
    margin-bottom: 0rem;
    background: $gray-800;
    border-bottom: 0px solid rgba(0, 0, 0, 0.125);
    color: $nep-dark-thick;
    display: block;
    width: 100%;
    font-size: 1rem;
    font-weight: bold;
}

.card {
    margin-top: 0px;
}

.card-main {
    border: 0;
}

.card .card {
    margin-top: 0px;
    border: 0;
    padding: 0 1rem;
}

.card .card .card-header {
    padding: 0;
    background-color: transparent;
    color: $nep-dark-thick;
    font-weight: 600;
    margin: 1rem 0 0.5rem 0;
    border-bottom: 1px solid $nep-light-border;
}

.p-datatable {
    display: block;
    width: 100%;
}

.btn {
    display: inline-block;
    margin-bottom: 0;
    border: 1px solid transparent;
    background-image: none;
    cursor: pointer;
    font-weight: 14px;
    outline: none;
    text-align: center;
    touch-action: manipulation;
    vertical-align: middle;
    white-space: nowrap;
    padding: var(--button-padding-base-vertical, 4px) var(--button-padding-base-horizontal, 16px);
    border-radius: 3px;
    border-radius: var(--button-border-radius, 3px);
    font-size: 14px;
    font-size: var(--button-font-size-base, 14px);
    line-height: 1.42857143;
    height: 32px;
    user-select: none;
}

.btn-primary {
    border-color: transparent;
    background-color: $nep-primary;
    background-color: var(--primary-color, $nep-primary);
    color: $nep-white-secondary;
    transition: background 0.15s ease-in-out;
}

.btn-primary:hover {
    background-color: $nep-darker-blue;
    background-color: var(--primary-color-dark-btn-hover, $nep-darker-blue);
    color: $nep-white-secondary;
}

button.btn.btn-primary.mar-0 {
    margin: 0 0.3rem 0 -0.05rem;
}

.btn-primary-round {
    border-color: transparent;
    background-color: $nep-primary;
    background-color: var(--primary-color, $nep-primary);
    color: $nep-white-secondary;
    transition: background 0.15s ease-in-out;
    height: 24px;
    width: 24px;
    border-radius: 50%;
    margin-top: 1px;
    outline: none !important;
}

.btn-primary-round:hover {
    background-color: $nep-darker-blue;
    background-color: var(--primary-color-dark-btn-hover, $nep-darker-blue);
    color: $nep-white-secondary;
}

.btn-primary-round.dropdown-toggle:focus {
    box-shadow: none;
    border-color: transparent !important;
}

.header {
    display:inline-flex;
    align-items:center;
    height: 60px;
    vertical-align: middle;
}

.header a {
    float: left;
    color: #9E9E9E;
    text-align: center;
    text-decoration: none;
    font-size: 18px;
}

.vl {
    border-left: 1px solid #e7e9ed;
    height: 20px !important;
    opacity: 0.96;
    border-radius: 1px !important;
    margin-top: 6px !important;
    margin-left: 12px;
    margin-right: 12px;
}

.header a.logo {
    font-weight: bold;
    color: black !important;
    padding-left: 20px !important;
}

.header-right {
    float: right;
    margin-top: -5px;
}

.header-right a {
    padding: 0px;
}

.btn-info {
    color: #111;
    background-color: #dfe2e6 !important;
    border-color: #dfe2e6 !important;
    margin: 0.2rem 0.3rem;
    padding: 0.2rem 0.8rem;
    font-size: 0.9rem;
    border-radius: 0;
}

.btn-info:hover {
    color: #111;
}

.btn-close {
    color: $nep-white-secondary;
    background-color: #117a8b;
    border-color: #117a8b !important;
    margin: 0.2rem 0.3rem;
    padding: 0.2rem 0.8rem;
    font-size: 0.9rem;
    border-radius: 0;
}

.btn-clear {
    color: $nep-white-secondary;
    background-color: #ea5959;
    border-color: #ededed !important;
    margin: 0.2rem 0.3rem !important;
    padding: 0.2rem 0.8rem;
    font-size: 0.9rem;
    border-radius: 0;
}

.add-control-btn .btn {
    margin: 0.6rem 0.5rem 0rem 0rem;
}

.form-margin {
    margin: 1rem 0;
}

.btn-warning {
    background: #dfe2e6 !important;
    color: $nep-dark-thick;
    margin: 0.2rem 0.3rem;
    border-color: #dfe2e6;
    border-radius: 0;
    padding: 0.175rem 0.75rem !important;
    font-size: 14px;
    min-width: 70px;
}

.btn-warning:not(:disabled):not(.disabled):active,
.btn-warning:not(:disabled):not(.disabled).active,
.show>.btn-warning.dropdown-toggle {
    color: $nep-white-secondary;
    background-color: #ff5959;
    border-color: #ff5959;
}

.btn:not(:disabled):not(.disabled):active:focus,
.btn:not(:disabled):not(.disabled).active:focus,
.show>.btn.dropdown-toggle:focus {
    box-shadow: none;
    border-color: transparent !important;
}

.btn:focus,
.btn.focus {
    box-shadow: none;
    border-color: transparent !important;
}

.btn-warning:hover {
    color: #111;
    background-color: #c5c5c5 !important;
    border-color: $nep-light-border;
}

.btn-back {
    background: transparent;
    color: #0056b3 !important;
    margin: 0;
    padding: 0rem 0.8rem !important;
    font-size: 0.8rem;
    border-color: transparent;
    border-radius: 0;
}

a.btn.btn-back .fa {
    font-size: 0.7rem;
}

a.btn.btn-back:hover {
    background: transparent;
    color: #00a4d8 !important;
}

.btn-export {
    color: $nep-white-secondary !important;
    background-color: #504e4e;
    border-color: transparent !important;
    margin: 0;
    padding: 0.1rem 0.2rem;
    font-size: 1rem;
    border-radius: 0;
    line-height: 1.2;
}

.btn-export:hover {
    background: $nep-text-color-blue;
}

input[type="text"],
select {
    background: $nep-white;padding: 0.1rem 0.75rem;
}

.form-group {
    margin: 1rem 0rem 0rem 0rem !important;
}

#login-form-id {
    margin-top: 2rem;
}

.welcome {
    padding: 0px;
    padding: 8px;
    font-size: 2rem;
    color: #03080d;
    font-weight: bold;
    background: $nep-darker-blue;
}

.welcome span {
    color: $nep-dark-blue;
}

.color-blue {
    color: #000811;
}

hr#login-hr {
    display: block;
    height: 1px;
    border: 0;
    border-top: 2px solid $nep-skyblue-light;
    margin: 0em 0 0.5rem 0;
    padding: 0;
}

.login-card {
    box-shadow: 0px 2px 50px #292727bf;
    background-color: #ffffffe6;
    border: 0;
}

.btn-login {
    width: calc(100% - 0px);
    margin: 0 auto;
    background: $nep-primary;
    color: $nep-white-secondary;
}

.form-control:focus {
    box-shadow: none;
    outline: 0;
}

.login-card input {
    border: 0px solid #ced4da;
    border-bottom: 2px solid #888;
    background-color: #f3f2f2;
}

span.color-blue.text-center {
    font-family: monospace;
}

.btn-secondary {
    border: 0;
}

.btn-secondary:not(:disabled):not(.disabled):active,
.btn-secondary:not(:disabled):not(.disabled).active,
.show>.btn-secondary.dropdown-toggle {
    color: $nep-light-border;
    background-color: transparent;
    border-color: transparent;
    border: 0;
    box-shadow: none !important;
}

.btn-secondary:hover {
    color: rgba(255, 255, 255, 0.75);
    background-color: transparent;
    border-color: transparent;
}

.btn-secondary:focus,
.btn-secondary.focus {
    outline: 0;
    box-shadow: none;
}

.login-card .card-body {
    margin: 15px 0;
}

.login-card input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
    background-color: $nep-white-secondary !important;
    background-image: none !important;
}

.padd-left-right-0 {
    padding-left: 0;
    padding-right: 0;
}

.login-page {
    background: $nep-light-border;
    padding: 15px 25px;
}

// #bg {
//     background-image: url("assets/dist/images/boxesbg.svg"), linear-gradient(180deg, #021155, #39195E 90%);
//     background-repeat: no-repeat;
//     background-size: cover;
//     background-position: center;
//     min-height: 100%;
//     min-width: 1024px;
//     width: 100%;
//     height: auto;
//     position: fixed;
//     top: 0;
//     left: 0;
// }

.login-form {
    padding-top: 40px;
    width: 25rem;
    height: 30rem;
    z-index: 222;
}

#login-form-id label {
    color: #01060c;
}

.navbar {
    padding: 0 1rem;
}

.dataTables_length select {
    display: inline-block;
    width: auto;
    max-width: 10rem;
}

.dataTables_filter {
    display: flex;
    justify-content: flex-end;
}

.dataTables_filter input {
    display: inline-block;
    width: auto;
    line-height: 0;
}

.dataTables_length label {
    display: block;
    margin-bottom: 0.5rem;
}

.dataTables_filter label {
    display: contents;
    margin-bottom: 0.5rem;
}

.dataTables_paginate {
    display: flex;
    justify-content: flex-end;
}

.table th,
.table td {
    padding: 0.25em 0.5em;
}

// .p-treetable .p-treetable-thead>tr>th,
// .p-treetable .p-treetable-tbody>tr>td,
// .p-treetable .p-treetable-tfoot>tr>td {
//     padding: 0.1em 0.5em !important;
// }
.p-datatable-emptymessage {
    text-align: center;
}

.page-item.active .page-link {
    z-index: 1;
    color: $nep-white-secondary;
    background-color: #504e4e;
    border-color: #504e4e;
}

.page-link:hover,
.page-item.disabled .page-link:hover {
    z-index: 2;
    color: $nep-white-secondary !important;
    text-decoration: none;
    background-color: $nep-dark-blue !important;
    border-color: #dee2e6;
}

@media (min-width: 992px) {
    .navbar-expand-lg .navbar-nav .nav-link {
        padding-right: 1.3rem;
        padding-left: 0rem;
    }
}

.add-fund-component .form-group label,
.add-user-component .form-group label,
.add-firm-component label,
.add-gorup-component label,
.add-user-component label,
.report-component label {
    display: block;
    margin-bottom: 0rem;
    text-align: left;
}

.table thead th:last-child,
.table tbody td:last-child {
    text-align: center;
}

h5 {
    border-bottom: 1px solid $nep-light-border;
    padding: 9px 0px;
    margin: 0.5rem 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.Sub-Title h5 {
    border-bottom: 1px solid $nep-light-border;
    padding: 9px 0px;
    margin: 0.5rem 0 1rem 0;
    font-size: 0.8rem;
    font-weight: 600;
}
.tab-section {
    margin: 1rem 0;
}

.ngb-tabset {
    display: block;
    margin: 1.5rem 0;
}

.ngb-tabset .tab-content {
    padding: 0.5rem;
    border: 1px solid $nep-light-border;
    border-top: 0;
    margin: 0;
}

.ngb-tabset .nav-tabs .nav-link.active,
.ngb-tabset .nav-tabs .nav-item.show .nav-link {
    color: $nep-white-secondary;
    background-color: $nep-skyblue-light;
    border-color: #dee2e6 #dee2e6 $nep-white-secondary;
    font-weight: 600;
}

.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    background: $gray-800;
    color: $nep-dark-thick;
}

.text-danger p {
    margin: 0;
}

.add-control-btn .btn-warning {
    background: #009775;
    color: $nep-white-secondary !important;
    border: 0;
    border-radius: 0;
    font-size: 0.8rem;
    margin: 0 0.3rem 0 0;
}

.add-control-btn a.btn.btn-info {
    background: #dfe2e6;
    color: $nep-dark-thick;
    margin: 0.2rem 0.3rem;
    padding: 0rem 0.8rem !important;
    font-size: 0.9rem;
    border-color: #dfe2e6;
    border-radius: 0;
}

.add-control-btn .btn-edit {
    background: transparent;
    color: $nep-dark-thick;
    border: 0;
    border-radius: 0;
    font-size: 0.8rem;
    margin: 0 0.3rem;
}

.add-control-btn .btn-detail {
    background: transparent;
    color: $nep-dark-thick;
    border: 0;
    border-radius: 0;
    font-size: 0.8rem;
    margin: 0 0.3rem;
}

.add-control-btn .btn:hover {
    background: #0198dd;
    color: $nep-white-secondary !important;
}

.add-control-btn a.btn.btn-info:hover {
    background: #ba0c2f;
}

.btn.btn-remove {
    background: transparent;
    color: #ba0c2f !important;
    padding: 0rem 0.5rem;
    font-size: 0.9rem;
    line-height: 1;
}

.btn-download {
    background: transparent;
    color: $nep-dark-thick !important;
    padding: 0rem 0.5rem;
    font-size: 0.9rem;
    line-height: 1;
}

.card>.card-header-main .btn.btn-primary .fa {
    display: inline-block;
    margin-right: 3px;
}

table.dataTable tbody th,
table.dataTable tbody td {
    padding: 3px 10px !important;
}

table.dataTable thead th,
table.dataTable thead td {
    padding: 5px 10px !important;
    border-bottom: 0px solid #111;
}

table.dataTable.no-footer {
    border-bottom: 0px solid #111;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    box-sizing: border-box;
    display: inline-block;
    min-width: 1.5em;
    padding: 0em 0em;
    margin-left: 2px;
    text-align: center;
    text-decoration: none !important;
    cursor: pointer;
    color: $nep-blue-333 !important;
    border: 0px solid transparent;
    border-radius: 0px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:active {
    outline: none;
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, $nep-dark-blue), color-stop(100%, #81cbe6));
    background: -webkit-linear-gradient(top, $nep-dark-blue 0%, #81cbe6 100%);
    background: -moz-linear-gradient(top, #2b2b2b 0%, #0c0c0c 100%);
    background: -ms-linear-gradient(top, #2b2b2b 0%, #0c0c0c 100%);
    background: -o-linear-gradient(top, #2b2b2b 0%, #0c0c0c 100%);
    background: linear-gradient(to bottom, $nep-dark-blue 0%, #81cbe6 100%);
    box-shadow: inset 0 0 1px #111;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    color: white !important;
    border: 0px solid #111;
    background-color: transparent;
    padding: 0em 0em;
}

.dropdown-menu {
    padding: 0;
}

.add-user-component table.dataTable thead th:last-child,
.add-user-component table.dataTable tbody td:last-child {
    text-align: center !important;
}

.page-link {
    padding: 0.2rem 0.5rem;
}

.dataTables_wrapper {
    position: relative;
    clear: both;
    zoom: 1;
    background: #fafbfb;
    padding: 0.5rem;
}

.sidenav {
    background: #f5f5f5;
    color: $nep-dark-link;
    overflow-y: auto;
    max-height: 500px;
    margin-top: 0px;
}

// .sidenav::-webkit-scrollbar {
//     width: 0.5em;
// }
// .sidenav::-webkit-scrollbar-track {
//     -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
//     border-radius: 10px;
//     background-color: #f5f5f5;
// }
// .sidenav::-webkit-scrollbar-thumb {
//     background-color: $nep-dark-blue;
//     border-radius: 10px;
//     outline: 1px solid $nep-slate-grey;
// }
.sidenav li a {
    color: $nep-dark-link;
    padding: 0rem 0rem;
}

.list-group-item.disabled,
.list-group-item:disabled {
    color: $nep-white-secondary;
    background-color: transparent;
    font-weight: bold;
}

.list-group-item {
    position: relative;
    display: block;
    padding: 0.5rem 0.25rem;
    margin-bottom: -1px;
    background-color: transparent;
    border: 0px solid rgba(0, 0, 0, 0.125);
    border-bottom: 1px solid #e5e5e5;
}

table.dataTable {
    background-color: $nep-white-secondary;
}

.alert-dismissible .close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0.1rem 0.25rem;
    color: inherit;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
    padding: 0.2rem 1rem;
    max-width: 403px;
    margin: 0.5rem auto;
    border-radius: 0;
}

.detail-fund-component .detail-page-text,
.detail-firm-component .detail-page-text,
.detail-portfolio-component .detail-page-text,
.detail-deal-component .detail-page-text,
.detail-user-component .detail-page-text,
.detail-pipeline-component .detail-page-text {
    text-align: right;
    color: #00a4d8;
}

.detail-deal-component .detail-page-text label {
    font-weight: bold;
}

.detail-fund-component .card-header,
.detail-firm-component .card-header,
.detail-portfolio-component .card-header {
    text-align: left;
}

.detail-sec {
    text-align: left;
    display: block;
    width: 100%;
    color: $nep-blue-333;
    word-break: break-word;
}

span.detail-sec a {
    color: $nep-dark-blue !important;
    cursor: pointer;
}

span.detail-sec a label {
    cursor: pointer;
}

.card-body .form-control {
    border: 0px solid #ced4da;
    border-radius: 0rem;
    border-bottom: 1px solid #E6E6E6;
    font-size: inherit;
    padding: 0;
}

.date-picker-input {
    border-bottom: 1px solid #b7b7b7 !important;
}

textarea.form-control {
    border: 1px solid #efebeb !important;
}

.p-button-text-icon-left .p-button-text {
    padding: 0.2em 0.5em 0.2em 0.5em;
    font-size: 0.9rem;
}

.card-body .form-control[readonly] {
    background: transparent;
}

.card-body .input-group-text {
    background-color: transparent;
    border: 0px solid #ced4da;
    border-bottom: 1px solid #d3dce0;
}

.p-datatable-tablewrapper,
.p-datatable-wrapper {
    overflow: auto;
}

.p-datatable-tablewrapper>table,
.p-datatable-wrapper>table {
    table-layout: auto;
}

.font-white {
    color: $nep-white-secondary !important;
}

.p-dropdown label.p-dropdown-label {
    cursor: pointer;
    margin: 0;
}

.p-widget-header {
    display: block;
    width: 100%;
    text-align: center;
    color: $nep-dark-thick;
    font-weight: normal;
}

.p-multiselect .p-widget-header {
    display: block;
    width: 100%;
    text-align: left;
}

table th,
.p-datatable .p-datatable-thead>tr>th,
.p-datatable .p-datatable-thead>tr>th {
    white-space: nowrap;
    color: #000000;
    font-weight: 500;
    font-size: 14px;
    font-family: "Helvetica Neue LT W05_65 Medium", Arial, Verdana, Tahoma, sans-serif;
}

table td,
.p-datatable .p-datatable-tfoot>tr>td,
.p-datatable .p-datatable-data>tr>td {
    font-size: 14px;
    font-weight: normal;
}

td.p-datatable-emptymessage {
    background: #dcf2fb;
    color: $nep-blue-333;
    text-align: center !important;
}

.p-datatable th.p-state-default {
    background: $nep-base-grey !important;
    border-color: $nep-grey-700 !important;
    color: $nep-dark-thick;
}

.table thead th {
    vertical-align: bottom;
    padding: 14px 10px;
    border: 1px solid $nep-grey-700;
    text-align: center;
    background-color: $nep-base-grey;
    color: $nep-text-grey;
    /* text-align: left!important; */
}

.search-box {
    padding: 0 0.3rem;
    max-width: 100%;
    margin-top: 0.1rem;
    line-height: 1.7;
}

// table tbody>tr>td {
//    // background-color: inherit !important;
//     // padding: 0.25em 0.5em !important;
//     padding: 12px 16px !important;
// }

.p-growl-title {
    font-weight: bold;
    padding: 0 0 0.5em 0;
    display: block;
    text-align: left;
}

.p-growl-message p {
    font-weight: normal;
    text-align: left;
}

.table-internal {
    margin: 1rem 0;
}

.p-button-icon-only .p-button-text,
.p-button-text-empty .p-button-text {
    padding: 0.45em;
    text-indent: -9999999px;
}

.p-autocomplete-dd input,
.pi-calendar.pi-calendar-w-btn input {
    position: relative;
    width: 100%;
    font-size: 14px !important;
    line-height: 1.5;
    color: #000000;
    background-color: $nep-white-secondary;
    background-clip: padding-box;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    /*border: 0px solid;
	*/
    height: 30px;
}

.p-autocomplete-dd .p-autocomplete-dropdown,
.p-calendar .p-calendar-button {
    position: absolute;
    right: 0px;
}

.p-calendar {
    width: 100%;
}

.p-multiselect {
    position: relative;
    display: block;
}

.p-autocomplete {
    width: 100% !important;
}

.p-multiselect .p-multiselect-label {
    text-align: left;
    margin-bottom: 0rem;
    padding: 0.3rem 0.3rem !important;
    color: #495057;
    margin-top: -0.1em;
    font-size: 14px;
}

.p-multiselect .p-multiselect-trigger {
    cursor: pointer;
    width: 2em !important;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    padding: 0 0.25em;
    border: none;
    color: $nep-white;
    background: $nep-white-secondary;
    -webkit-transition: background-color 0.2s;
    -moz-transition: background-color 0.2s;
    transition: background-color 0.2s;
}

.report-component .report-edit-dropdown .p-widget-header {
    background: $nep-bg-light;
    background: -moz-linear-gradient(top, $gray-800 0%, $gray-800 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, $nep-dark-blue), color-stop(100%, $gray-800));
    background: -webkit-linear-gradient(top, $gray-800 0%, $gray-800 100%);
    background: -o-linear-gradient(top, $gray-800 0%, $gray-800 100%);
    background: -ms-linear-gradient(top, $gray-800 0%, $gray-800 100%);
    background: linear-gradient(to bottom, $gray-800 0%, $gray-800 100%);
}

.p-calendar.p-calendar-w-btn input::placeholder,
.p-dropdown p-inputtext p-placeholder {
    /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: $nep-text-color;
    opacity: 1;
    font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;
    /* Firefox */
}

.p-table {
    /* margin: 0rem 0 1rem 0; */
    display: block;
}

.p-table .p-widget-header {
    text-align: left;
    font-size: 12px;
    font-weight: 700;
    color: $nep-text-grey;
}

.p-multiselect .p-multiselect-panel .p-widget-header {
    background: $nep-bg-light;
    background: -moz-linear-gradient(top, $gray-800 0%, $gray-800 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, $nep-dark-blue), color-stop(100%, $gray-800));
    background: -webkit-linear-gradient(top, $gray-800 0%, $gray-800 100%);
    background: -o-linear-gradient(top, $gray-800 0%, $gray-800 100%);
    background: -ms-linear-gradient(top, $gray-800 0%, $gray-800 100%);
    background: linear-gradient(to bottom, $gray-800 0%, $gray-800 100%);
    text-align: left;
    font-weight: bold;
}

.p-multiselect .p-multiselect-panel {
    box-shadow: 0 0 0;
    border-radius: 0;
    min-width: 100% !important;
}

.p-autocomplete .p-autocomplete-panel {
    box-shadow: 1px 3px 12px #00000014;
    border-radius: 0;
}

.p-chkbox .p-chkbox-box {
    border: 1px solid #c0c0c0;
    margin-top: -6px;
    width: 1.25em;
    height: 1.25em;
}

.p-autocomplete-panel .p-autocomplete-list-item {
    font-size: 14px;
}

.upi-state-highlight {
    background-color: #d3d6da52;
    color: $nep-dark-thick;
}

.p-state-highlight label {
    color: $nep-dark-thick;
}

.p-multiselect-panel .p-multiselect-item {
    font-size: 14px;
}

.p-multiselect-panel .p-multiselect-item label {
    color: $nep-dark-thick;
}

.p-multiselect-panel .p-multiselect-close {
    color: $nep-white-secondary;
}

.p-multiselect:not(.p-state-disabled):hover a {
    color: $nep-white-secondary;
}

.p-state-default {
    border: 0px solid;
    border-bottom: 1px solid #b7b7b7;
    background-color: $nep-white;
    color: $gray-600;
    border-radius: 0;
}

.p-treetable {
    display: block;
    margin-top: 1rem;
}

.modal-header {
    background: $gray-800;
    color: $nep-white-secondary;
    border-radius: 0;
    // padding: 0.2rem 1rem;
}

.p-inputtext {
    padding: 0em;
}

.p-widget-overlay {
    background-color: #0a0a0a99;
    opacity: 1;
    filter: Alpha(Opacity=50);
    color: $nep-white-secondary;
}

.loading-input-controls {
    position: absolute;
    right: 3.9rem;
    top: 0.1rem;
    width: 20px;
    color: $nep-dark-thick;
}

.loading-input-controls-manual {
    position: relative;
    width: 39px;
    color: #0a0a0a;
    font-size: 1rem;
    font-weight: bold;
    display: inline-block;
    line-height: 1.2;
    vertical-align: middle;
}

.comment-section .comment-btn {
    position: absolute;
    right: 0px;
    top: 0;
    color: #0088bc !important;
    cursor: pointer;
}

.p-overlaypanel-close {
    position: absolute;
    top: 0em;
    right: 0em;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    /* line-height: 0;
	*/
    font-size: 0.7rem;
    color: $nep-white-secondary !important;
    background-color: $nep-white-secondary !important;
}

.p-overlaypanel-content {
    padding: 1em 1.1em 0 1rem;
    background-color: transparent;
    background: #e6e6e6b8;
}

.p-overlaypanel .p-overlaypanel-close:hover {
    border-color: #c0c0c0;
    background-color: #b7b7b7;
    color: #212121;
}

.p-widget-content a {
    color: $nep-text-color-blue;
    /*text-transform: uppercase;
	*/
}

.p-treetable-wrapper td,
.p-treetable-wrapper th,
.p-datatable .p-datatable-thead>tr>th {
    text-align: left;
    /* padding-left: 20px!important; */
}

.p-treetable-wrapper td:first-child,
.p-treetable-wrapper th:first-child {
    text-align: left;
}

.modal-header h4 {
    font-size: 1rem;
    color: #212121;
}

.power-off {
    font-size: 1.5rem;
    color: #e67070;
}

form label {
    margin-top: 2px;
}

.report-edit-dropdown form {
    padding: 0.5rem 0 0 0;
}

.report-edit-dropdown {
    border: 1px solid #f2f1f1;
    padding: 0rem 0.8rem;
    margin-bottom: 1rem;
    background: $nep-light-bg;
    border-radius: 2px;
    box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
}

.report-edit-adjustment {
    padding: 0rem 0.8rem;
    margin-bottom: 1rem;
    background: $nep-light-bg;
    margin-right: .1%;
    margin-left: .1%;
    border-radius: 2px;
    box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
}

.report-edit-dropdown>a.btn {
    padding: 0;
    margin: 0.3rem 0 0 0;
    line-height: 1.3;
    font-size: 0.9rem;
}

span.filter-text {
    font-size: 0.9rem;
    margin-left: 5px;
    display: inline-block;
}

.margin-left-0 {
    padding-left: 0;
}

.horizontal-menu {
    overflow-x: hidden !important;
    white-space: nowrap;
    max-width: 100%;
    min-height: 28px;
}

.horizontal-child1 {
    float: left;
    width: 15px;
    z-index: 999;
    margin-top: -0.15rem;
}

.horizontal-child2 {
    float: right;
    width: 15px;
    z-index: 999;
    margin-top: -0.15rem;
}

.horizontal-child1 a,
.horizontal-child2 a {
    color: $nep-gray-dark !important;
    opacity: 1;
    /* background: #504e4ecc;
	*/
    padding: 0.2rem 0.1rem;
    font-size: 1.1rem;
    z-index: 999;
}

// .horizontal-menu::-webkit-scrollbar {
//     height: 0em;
//     cursor: pointer;
// }

/*.horizontal-menu::-webkit-scrollbar-track {
	-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
	border-radius: 10px;
}
.horizontal-menu::-webkit-scrollbar-thumb {
	background-color: $nep-deepskyblue;
	outline: 1px solid $nep-slate-grey;
	border-radius: 10px;
}
*/

.tab-content {
    margin: 1rem 0;
}

.horizontal-menu .p-togglebutton.p-button.p-state-default,
.horizontal-menu .p-selectbutton .p-button.p-state-default {
    border: 0;
    color: $nep-dark-blue;
}

.horizontal-menu .p-togglebutton.p-button.p-state-active,
.horizontal-menu .p-selectbutton .p-button.p-state-active {
    border: 0;
    background: transparent;
    color: #999;
}

.horizontal-menu.p-selectbutton.p-button:not(.p-state-disabled):not(.p-state-active):hover {
    border-color: transparent;
    background-color: transparent;
    color: #0056b3;
    text-decoration: underline;
    border-bottom: 1px solid $nep-skyblue;
}

.report-component .nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    color: #131212;
    padding: 1rem 0.8rem;
}

.report-component .nav-tabs .nav-link.active,
.report-component .nav-tabs .nav-item.show .nav-link {
    color: #0198dd;
    background-color: $nep-white;
    border-color: $nep-white $nep-white $nep-white-secondary;
    border-bottom: 4px solid #09d;
    font-weight: bold;
}

.table-header-ui {
    background: $nep-bg-light;
    background: linear-gradient(to bottom, $nep-bg-light 0%, $nep-bg-light 100%);
}

.charts-parent-div {
    background-color: $nep-light-bg;
}

.chart-bg {
    background: $nep-white-secondary;
    border-radius: 2px;
    /* box-shadow: 0 3px 1px -2px rgba(0,0,0,.2), 0 2px 2px 0 rgba(0,0,0,.14), 0 1px 5px 0 rgba(0,0,0,.12);
	*/
    padding: 0 1rem;
    margin: 1rem;
}

.chart-section {
    max-width: 420px;
    margin: 0rem auto;
}

.line-chart-section {
    /* max-width: 900px;
	*/
    margin: 0rem auto;
}

.range-in {
    max-width: 33%;
    font-size: 0.9rem;
    border-right: 1px solid $nep-skyblue !important;
    text-align: center;
    border-bottom: 1px solid $nep-skyblue !important;
}

footer {
    /*background: #e2e2e2;
	*/
    border-top: 1px solid #ccc;
    padding-top: 0px;
    background: $nep-white-secondary;
    position: absolute;
    bottom: 0;
    width: 100%;
    padding-bottom: 8px;
}

footer.footer {
    position: fixed;
}

footer p {
    margin-top: 14px;
    margin-bottom: 3px !important;
}

.body-content {
    min-height: 100%;
    position: relative;
    /* padding-bottom: 60px; */
}

.footer-text {
    text-align: center;
    color: $nep-white-secondary;
    padding: 0.3rem 0;
}

.report-menu {
    padding-top: 2px;
}

.report-menu button,
.report-menu a {
    font-size: 0.8rem;
    cursor: pointer;
}

.report-menu .btn-secondary {
    color: $nep-dark-thick;
    background-color: $nep-bg-light;
    border-color: $nep-dark-highlight;
    border: 0;
    border-radius: 0;
    padding: 3px 8px;
}

.report-menu .btn-secondary:not(:disabled):not(.disabled):active,
.report-menu .btn-secondary:not(:disabled):not(.disabled).active,
.report-component .show>.btn-secondary.dropdown-toggle {
    color: $nep-blue-333 !important;
    background-color: transparent;
    border-color: transparent;
    border: 0;
    box-shadow: none !important;
    outline: 0;
}

.report-menu .btn-secondary:focus,
.report-menu .btn-secondary.focus {
    box-shadow: none;
}

.report-menu .dropdown.report-menu button {
    padding: 0;
    margin: 0;
}

.forgot-password {
    text-align: left;
    padding-left: 0.4rem;
    text-decoration: underline;
}

.error-msg-login {
    text-align: center;
    padding: 0rem 0 1rem 0;
}

a.expand {
    position: absolute;
    top: 23px;
    right: 45px;
    font-size: 1.2rem;
}

a.expand:hover {
    color: $nep-skyblue !important;
}

@media (max-width: 481px) {
    a.expand {
        display: none;
    }
}

.table-data-left {
    text-align: left !important;
}

.table-data-right {
    text-align: right !important;
}

.table-data-center {
    text-align: center !important;
}

.p-growl-message,
.p-growl-image,
.p-growl-icon-close {
    color: $nep-white-secondary;
}

.p-growl-icon-close {
    font-size: 1rem;
}

.ul-none-style {
    list-style: none;
    margin: 0;
    padding: 0;
}

.model-popup label {
    font-size: 0.7rem;
}

.model-popup .form-group {
    margin: 0.8rem 1rem 0.3rem 1rem;
}

.p-dialog-titlebar.p-widget-header.p-helper-clearfix.p-corner-top {
    background: $nep-white-secondary;
    text-align: left;
    padding: 0.3em 1.5em;
}

.p-dialog .p-dialog-titlebar-icon span {
    display: block;
    margin: 0;
    font-size: 1.75em;
}

.p-dialog .p-dialog-titlebar-icon:hover {
    border-color: #c0c0c0;
    background-color: transparent;
    color: #212121;
}

.close {
    float: right;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: rgba(0, 0, 0, 0.85);
    text-shadow: 0 1px 0 $nep-white-secondary;
    opacity: 1;
}

.close:hover {
    color: $nep-white-secondary;
}

select.form-control:not([size]):not([multiple]) {
    height: calc(1.35rem + 2px);
}

i.icons-ban {
    color: #d21f1f;
}

i.cross-color {
    color: #ff0d0d;
}

i.check-color {
    color: #038c03;
}

.box-query {
    border: 1px solid #eaeaea;
    /* box-shadow: 10px 10px 10px 10px inset $nep-light-border;
	*/
    padding: 6px;
    background: $nep-light-border;
}

.CodeMirror-scroll {
    overflow: scroll !important;
    margin-bottom: -30px;
    margin-right: -30px;
    padding-bottom: 30px;
    height: 100%;
    outline: none;
    position: relative;
    font-size: 0.9rem;
}

.btn-preview:disabled,
.btn-clear1:disabled,
.btn-export:disabled,
.btn-reset:disabled,
.btn-primary:disabled,
.btn-warning:disabled {
    color: $nep-dark-thick !important;
    background-color: $nep-light-border;
    border-color: $nep-light-border !important;
}

.btn-preview {
    color: $nep-white-secondary;
    background-color: #0181b2;
    border-color: #0181b2 !important;
    margin: 0rem 0.3rem;
    padding: 0rem 0.8rem !important;
    font-size: 0.9rem;
    border-radius: 0;
}

.btn-clear1 {
    color: $nep-white-secondary;
    background-color: #dc6d6d;
    border-color: #dc6d6d !important;
    margin: 0rem 0.3rem;
    padding: 0rem 0.8rem !important;
    font-size: 0.9rem;
    border-radius: 0;
}

.btn-export {
    color: $nep-white-secondary !important;
    margin: 0 5px 0 0;
    padding: 0.12rem 0.6rem;
    font-size: 14px;
    border-radius: 0;
    line-height: 1.5;
    height: 26px;
    min-width: 30px !important;
    background: $nep-skyblue-light;
    border-color: $nep-skyblue-light;
}

.portafolio-table a.btn-export {
    margin-top: 0;
}

.btn-reset {
    color: $nep-white-secondary;
    background-color: $nep-blue-border;
    border-color: #0198dd !important;
    margin: 0rem 0.3rem;
    padding: 0rem 0.8rem !important;
    font-size: 0.9rem;
    border-radius: 0;
}

.padd-0 {
    padding: 0 1rem !important;
}

.padd-lr-10 {
    padding: 0 15px 0 10px !important;
}

.p-multiselect-header .p-inputtext {
    padding: 0px 1.75rem 0px 0.5rem !important;
    width: 100%;
}

.p-multiselect-header.p-multiselect-filter-container.p-multiselect-filter-icon {
    position: absolute;
    top: 0.45em;
    left: 0.125em;
}

.red {
    color: rgb(210, 15, 70);
}

.scroll-table .p-datatable-wrapper {
    max-height: 600px;
    overflow-y: auto;
}

p-button-text-only .p-button-text {
    padding: 0.25em 1em;
    min-width: 45px;
}

th.table-data-right {
    text-align: center !important;
}

.text-danger {
    color: rgb(210, 15, 70) !important;
}

ul.list-non {
    padding-left: 15px;
}

.modal-content .text-danger {
    font-size: 0.6rem;
}

.modal-content {
    border: none !important;
}

.loading-main {
    width: 100px;
    position: absolute;
    color: $nep-white-secondary;
    top: 50%;
    left: 50%;
    -ms-transform: translateX(-50%) translateY(-50%);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.file-upload-section .p-button-text-icon-left .p-button-text {
    padding-top: 0.2em;
    padding-bottom: 0.2em;
    display: inline-block;
}

.file-upload-section .p-fileupload-buttonbar .p-button {
    vertical-align: middle;
    min-width: 85px;
}

.file-upload-section {
    margin-top: 20px;
    border: 1px solid $gray-800;
}

.file-upload-section .p-state-default {
    border: 0px solid;
    border-radius: 0;
    color: $nep-white;
    background: var(--primary-color, $nep-primary);
    -webkit-transition: background-color 0.2s;
    -moz-transition: background-color 0.2s;
    transition: background-color 0.2s;
    padding-top: 1px;
}

.p-dropdown-panel .p-dropdown-item {
    font-weight: 400;
    border: 0 none;
    cursor: pointer;
    text-align: left;
}

.p-dropdown-panel .p-dropdown-list {
    padding-left: 0em !important;
    padding-right: 0em !important;
    padding-top: 0.4em !important;
    padding-bottom: 0.4em !important;
    border: 0 none;
}

.file-upload-section .p-dropdown {
    display: inline-block;
    position: relative;
    cursor: pointer;
    vertical-align: middle;
    width: 100% !important;
}

.file-upload-section .p-corner-all {
    -moz-border-radius: 3px;
    -webkit-border-radius: 0;
    border-radius: 0;
}

.file-upload-section .p-widget-header {
    display: block;
    width: 100%;
    text-align: left !important;
}

.file-upload-section .p-progressbar {
    height: 1.8em;
    text-align: left;
    position: relative;
    overflow: hidden;
    top: 0;
}

.p-fileupload-choose:not(.p-state-disabled):hover,
.p-fileupload-choose.p-state-focus {
    background: var(--primary-color, $nep-primary);
    color: $nep-white;
    -webkit-transition: background-color 0.2s;
    -moz-transition: background-color 0.2s;
    transition: background-color 0.2s;
}

.p-fileupload-choose:not(.p-state-disabled):active {
    background: var(--primary-color, $nep-primary);
    color: $nep-white;
    -webkit-transition: background-color 0.2s;
    -moz-transition: background-color 0.2s;
    transition: background-color 0.2s;
}

.file-upload-section .p-progressbar-determinate .p-progressbar-label {
    text-align: center;
    height: 100%;
    width: 100%;
    position: absolute;
    display: none;
    font-weight: bold;
    padding-top: 2px;
    color: $nep-dark-thick;
    border: 1px solid $nep-light-border;
}

.file-upload-section button.p-button:focus,
.file-upload-section button.p-button:enabled:hover,
.file-upload-section button.p-fileupload-choose:not(.p-state-disabled):hover {
    background: var(--primary-color, $nep-primary);
    color: $nep-white;
    -webkit-transition: background-color 0.2s;
    -moz-transition: background-color 0.2s;
    transition: background-color 0.2s;
}

.file-upload-section button.p-button.p-widget.p-state-default.p-corner-all.p-button-icon-only:hover span {
    top: 13px;
    left: 13px;
}

.desktop-m-3 {
    margin-top: 1rem;
}

.text-info {
    color: $nep-blue-600 !important;
    font-size: 0.7rem;
}

.modal-dialog .card-body form {
    padding: 5px 15px;
}

.card>.card-header-main span.hide-on-mobile {
    display: inline-block;
    margin-left: 0px;
}


/*admin card counter*/

.card-counter {
    box-shadow: 2px 2px 10px #dadada;
    margin: 0px;
    padding: 20px 10px;
    background-color: $nep-white-secondary;
    height: 100px;
    border-radius: 0px;
    transition: 0.3s linear all;
    /*opacity:0.7;
	*/
}

.card-counter:hover {
    box-shadow: 4px 4px 20px #dadada;
    transition: 0.3s linear all;
}

.card-counter.primary {
    /*background-color: $nep-blue-border;
	*/
    /*background-color: #f1b53d;
	*/
    background-color: #e35205;
    color: $nep-white-secondary;
}

.numeric-number td {
    text-align: center !important;
}

.card-counter.danger {
    /*background-color: #66bb6a;
	*/
    background-color: #78be20;
    color: $nep-white-secondary;
}

.card-counter.success {
    /*background-color: #1f77b4;
	*/
    background-color: #009775;
    color: $nep-white-secondary;
}

.card-counter.info {
    /*background-color: #23b3b7;
	*/
    /*background-color: #3db9dc;
	*/
    background-color: $nep-blue-600;
    color: $nep-white-secondary;
}

.card-counter.info1 {
    /*background-color: #3db9dc;
	*/
    background-color: #5c068c;
    color: $nep-white-secondary;
}

.card-counter.commitment {
    /*background-color: #3db9dc;
	*/
    background-color: $nep-text-color-blue;
    color: $nep-white-secondary;
}

.card-counter.realized {
    /*background-color: #3db9dc;
	*/
    background-color: $nep-blue-600;
    color: $nep-white-secondary;
}

.card-counter i {
    font-size: 4em;
    opacity: 0.2;
}

.card-counter .count-numbers {
    position: absolute;
    right: 15px;
    top: 20px;
    font-size: 22px;
    display: block;
}

.card-counter .count-name {
    position: absolute;
    right: 15px;
    top: 65px;
    font-style: italic;
    text-transform: capitalize;
    opacity: 0.5;
    display: block;
    font-size: 16px;
}


/*admin card counter*/

.dashboard-head .chart-bg {
    margin: 0.3rem 0;
    padding: 0;
    box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
}

.dashboard-head .chart-bg .line-chart-section {
    padding: 0.8rem;
}

.dashboard-head h5 {
    padding-left: 15px;
}

.chart-bg .text-info {
    font-size: 1rem !important;
    color: $nep-blue-333 !important;
    text-align: center;
    font-weight: 600;
}

.blank-space {
    border-left: 1px solid #d5d5d5 !important;
    border-right: 1px solid #d5d5d5 !important;
    border-bottom: 1px solid $nep-white-secondary !important;
    background: $nep-white-secondary !important;
    max-width: 96px !important;
    border-top-color: $nep-white-secondary !important;
}

.cashflow-table th {
    font-weight: bold;
}


/*.cashflow-data table td:first-child, .cashflow-data table td:nth-child(2) {
	min-width: 200px;
	font-weight: bold;
}
*/

.cashflow-data table th {
    white-space: normal;
    /* font-weight: bold; */
}

.p-datatable-unfrozen-view table tbody td {
    text-align: right;
}

.cashflow-data table th:first-child {
    height: 49px;
}

.width-200 {
    width: 200px;
}

.width-150 {
    width: 150px;
}

.width-94 {
    min-width: 94px !important;
}

.width-30 {
    width: 30px;
}

.width-0 {
    width: 0px;
}

.realized-section {
    background: #52BE80 !important;
    color: $nep-white-secondary !important;
    font-weight: bold !important;
}

.unrealized-section {
    background: #EC7063 !important;
    color: $nep-white-secondary !important;
    font-weight: bold !important;
}

.cashflow-upload .p-autocomplete-dd input.p-corner-all {
    width: 100% !important;
    line-height: 1.8;
}

.d3-pie-bar-chart .slice {
    /*font-size: 12pt;
	*/
    fill: white;
    /* //svg specific - instead of color font-weight: bold;
	*/
}


/*for line chart*/

.d3-pie-bar-chart .axis path,
.axis line {
    fill: none;
    stroke: black;
    shape-rendering: crispEdges;
    /* The shape-rendering property is an SVG attribute, used here to make sure our axis and its tick mark lines are pixel-perfect. */
}

.d3-pie-bar-chart .line {
    fill: none;
    /*stroke: steelblue;
	*/
    stroke-width: 3px;
}

.d3-pie-bar-chart .dot {
    /*fill: white;
	*/
    /*stroke: steelblue;
	*/
    stroke-width: 1.5px;
}

.d3-pie-bar-chart .axis text {
    font-size: 11px;
}

.d3-pie-bar-chart .title {
    font-size: 15px;
}

.d3-pie-bar-chart .xAxis {
    font-size: 11px;
    fill: black;
}

.d3-pie-bar-chart .yAxis {
    font-size: 11px;
    /*fill: white;
	*/
}

.d3-pie-bar-chart table {
    border-collapse: collapse;
    border: 0px;
    color: #5c5558;
    font-size: 12px;
    text-align: right;
}

.d3-pie-bar-chart td {
    padding-left: 10px;
}

.d3-pie-bar-chart #lineChartTitle1 {
    font-size: 14px;
    fill: #0098cf;
    font-weight: bold;
    text-anchor: middle;
}

.d3-pie-bar-chart #lineChartTitle2 {
    font-size: 50px;
    fill: grey;
    text-anchor: middle;
    font-weight: bold;
    /*font-style: italic;
	*/
}

app-piebar-chart {
    display: block;
    width: 100%;
    padding: 15px;
}

.d3-pie-bar-chart svg {
    margin: 0 auto;
    max-width: 500px;
    display: block;
}

.legend-1 {
    width: 24px;
    height: 10px;
    background: #52BE80 !important;
    margin: 0 5px;
    display: inline-block;
}

.legend-2 {
    width: 24px;
    height: 10px;
    background: #EC7063 !important;
    margin: 0 5px;
    display: inline-block;
}

.cashflow-data .p-datatable-scrollable-view.p-datatable-frozen-view.ng-star-inserted {
    cursor: not-allowed;
}

tr.higlighted-row {
    background-color:
        /*#cecdcd !important*/
        #ebf8fc !important;
}

tr.higlighted-row td {
    font-weight: bold;
    background-color: rgb(235, 248, 252) !important;
}

.bg-por-row {
    background: #dcf2fb;
}

.bg-port-row {
    background: $nep-white-secondary;
    border: 1px solid #d4d4d4;
}

.kpi-pop-diff {
    border: 3px dotted $nep-light-border !important;
    margin-top: 20px !important;
}

.max-height-300 {
    max-height: 300px;
}


/*--Important-detail---*/

.card-body.important-detail-section {
    background: #c3c3c322;
    padding: 10px;
    border: 1px solid $nep-grey-700;
    line-height: 30px;
}

.important-detail {
    position: absolute;
    height: 100%;
}

.important-detail-outer {
    position: relative;
    overflow: hidden;
}

.dynamic-query td:first-child {
    text-transform: uppercase;
}

.chart-bg p-table {
    margin: 0rem 0 1rem 0;
    display: block;
    padding: 0 0 1rem 0;
}

.add-query-component .p-paginator .p-paginator-page {
    width: auto !important;
    padding: 0 0.5rem !important;
}

.loading-login-reset {
    width: 20px;
    margin: 0px auto;
    padding-top: 11px;
    display: block;
}


/*---Sweta---*/

.information {
    background: #f9f6e4;
    border: 1px solid #aa8c53;
    border-radius: 3px;
}

.email-id {
    border-bottom: 1px solid #fdb5b5;
    display: block;
    padding: 3px 5px;
    background: $nep-light-border;
}

.reset-pass-card {
    box-shadow: 0px 2px 10px #292727bf;
    background-color: #ffffffe6;
    border: 0;
}

.reset-password-section {
    margin-top: 10px;
}

.forgot-password-section {
    margin-top: 10px;
}

span.report-field .p-multiselect label.p-multiselect-label {
    padding: 0.1rem 0.3rem !important;
}

.p-multiselect {
    width: 100% !important;
    padding-left: 0.3rem;
    display: inline-flex;
}

.btn-text {
    text-transform: capitalize;
    vertical-align: top;
    padding: 1px 2px;
    margin: 5px 0;
    display: inline;
}

.icon {
    color: #111;
}

.spacing {
    padding: 4px 10px;
    border-bottom: 0 none;
}

.comment-btn span {
    font-weight: bold;
    color: $nep-text-color-blue;
    padding: 0px 14px 0 2px;
}

.gbp-info {
    color: #e35205;
    text-align: right;
    font-size: 14px;
}

.chart-header {
    border-bottom: 1px solid $nep-light-border;
    padding: 9px 0px;
    margin: 0.5rem 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.gbp-info i {
    padding: 0 3px;
}

.chart-donut {
    margin: 10px 90px 0 90px;
    padding: 0 1rem !important;
}

.help {
    color: #80cdf0;
}

.company-name {
    text-transform: uppercase !important;
}

.popup-bg {
    background: $nep-white-secondary;
}

.popup-title {
    border-bottom: 1px solid $nep-light-bg;
}

.comment-btn span.choose-value {
    padding: 3px;
    border: 1px solid $gray-600;
    border-radius: 4px;
    background: $nep-white-secondary;
}

p-inputswitch.switch.ng-untouched.ng-pristine.ng-valid {
    position: relative;
    top: 1px;
    left: 4px;
}

.search {
    position: relative;
}

.search input {
    text-indent: 3px;
}

.search .fa-search {
    position: absolute;
    top: 10px;
    right: 22px;
    font-size: 15px;
    color: $nep-icon-grey;
}

.p-datatable .p-datatable-thead>tr>th,
.p-datatable .p-datatable-thead>tr>th:hover {
    background-color: $nep-base-grey;
    border-color: $gray-600;
    /* color: #fff; */
}

.p-datatable .p-datatable-tfoot>tr>td {
    font-weight: bold;
    background: $gray-600;
}


/*Overlay*/

.p-widget-overlay {
    background: #030303b3 0% 0% no-repeat padding-box;
    opacity: 1;
}

.p-overlaypanel {
    background-color: $nep-white;
    color: #333333;
    padding: 0;
    border: 1px solid #c8c8c8;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
}

.p-overlaypanel .p-overlaypanel-content {
    padding: 0.571em 1em;
}

.p-overlaypanel .p-overlaypanel-close {
    background-color: #007ad9;
    color: $nep-white-secondary;
    // width: 1.538em;
    // height: 1.538em;
    // line-height: 1.538em;
    text-align: center;
    position: absolute;
    top: -0.769em;
    right: -0.769em;
    -moz-transition: background-color 0.2s;
    -o-transition: background-color 0.2s;
    -webkit-transition: background-color 0.2s;
    transition: background-color 0.2s;
}

.p-overlaypanel .p-overlaypanel-close:hover {
    background-color: #005b9f;
    color: $nep-white;
}

.p-overlaypanel .p-overlaypanel-close .p-overlaypanel-close-icon {
    line-height: inherit;
}

.p-overlaypanel:after {
    border-color: rgba(255, 255, 255, 0);
    border-bottom-color: $nep-white;
}

.p-overlaypanel:before {
    border-color: rgba(200, 200, 200, 0);
    border-bottom-color: #c8c8c8;
}

.p-overlaypanel.p-overlaypanel-flipped:after {
    border-top-color: $nep-white;
}

.p-overlaypanel.p-overlaypanel-flipped:before {
    border-top-color: #c8c8c8;
}


/*Fund detail*/

.border-1 {
    border: 1px solid #d5d3d3;
}

.border-radius-2 {
    border-radius: 3px;
}

.fund-detail-component .fund-detail,
.portfolio-detail-component .fund-detail {
    margin: 0 0 1rem 0;
    /* background: #e5f5fc; */
}

.fund-detail-component .fund-detail i.dollar,
.portfolio-detail-component i.dollar {
    font-size: 4rem;
    color: #c2c2c2;
}

.fund-detail-component .fund-detail .fund-name,
.portfolio-detail-component .fund-name {
    color: $nep-text-color-blue;
    font-size: 1.6rem;
    margin: 0.5rem 0;
}

.mar-bottom-15 {
    margin-bottom: 15px;
}

.chart-area .chart-title {
    background: $gray-800;
    border-bottom: 1px solid #c4c4c4;
    font-size: 1rem;
    overflow: hidden;
    
}

.portfolio-detail-component .chart-area .chart-title {
    background: $gray-800;
    border-bottom: 1px solid #DEDFE0;
    font-size: 1rem;
    overflow: inherit !important;
}

.chart-area .chart-content {
    min-height: 10%;
    max-height: 100%;
}

.chart-area .chart-content img {
    width: 98%;
    margin: 0.3rem;
}

.detail-border {
    border-bottom: 1px solid $gray-600;
    margin: 0 0.5rem;
    padding: 0.35rem 0;
}

.detail-border label {
    margin-bottom: 0;
    margin-top: 0;
    color: #002e5d;
    font-weight: bold;
}

.fund-footer {
    background: #f1f3f6;
    border-top: 1px solid #c4c4c4;
    font-size: 1rem;
    padding: 0.3rem;
    margin: 0 0 0 0;
    overflow: hidden;
}


/* .section1-height {
        min-height: 346px;
    } */

.section2-height {
    min-height: 200px;
}

.section3-height {
    min-height: 476px;
}

.custome-table .p-datatable-tablewrapper {
    min-height: 320px;
    border: 1px solid $nep-grey-700;
}

.section1-height .chart-wrapper .inner-wrapper {
    padding-bottom: 29%;
    min-height: 240px;
}

.custome-table .p-datatable .p-datatable-data>tr>td {
    border-width: 1px 1px 1px 0 !important;
}

.custome-table.p-datatable.p-datatable-data>tr>td.p-datatable-emptymessage {
    height: 289px !important;
}

.Description-bg {
    background: $nep-white-secondary;
}

.des-title {
    color: #002e5d;
    font-weight: bold;
    font-size: 0.9rem;
    border-bottom: 1px solid $nep-grey-700;
    margin: 0 2px;
}

.fund-detail-component tooltip {
    margin-left: 5px;
}

.Fund-section {
    max-height: 100%;
    overflow-y: auto;
    min-height: 30%;
    padding-left: 15px;
}

.fund-term-section {
    max-height: 412px;
    overflow-y: auto;
}

// .Fund-section::-webkit-scrollbar {
//     width: 0.3em;
// }
// .Fund-section::-webkit-scrollbar-track {
//     -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
//     border-radius: 8px;
// }
// .Fund-section::-webkit-scrollbar-thumb {
//     background-color: $nep-deepskyblue;
//     outline: 1px solid $nep-slate-grey;
//     border-radius: 10px;
// }
// .fund-term-section::-webkit-scrollbar {
//     width: 0.3em;
// }
// .fund-term-section::-webkit-scrollbar-track {
//     -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
//     border-radius: 8px;
// }
// .fund-term-section::-webkit-scrollbar-thumb {
//     background-color: $nep-deepskyblue;
//     outline: 1px solid $nep-slate-grey;
//     border-radius: 10px;
// }
feature .section3-height .p-datatable tbody {
    max-height: 296px;
    overflow-y: auto;
}

.section3-height .p-datatable-scrollable-body {
    max-height: 291px !important;
}

.higlighted-cell {
    background-color: #ebf8fc !important;
}

.calculated-value {
    color: #ebf8fc !important;
    /* border: 1px solid #d2d2d2;
	*/
    margin-right: 5px;
    font-size: 1.2rem !important;
}

.content-bg {
    background: $nep-white-secondary;
}

.info:hover,
.help:hover {
    cursor: pointer;
}

.cashflow tooltip {
    width: 500px;
}

.info-div {
    max-width: 600px;
    min-width: 150px;
    padding: 0;
}

.fund-term-section tooltip {
    float: right;
}

.report-chart div {
    width: 100%;
}

.m-top15 {
    margin-top: 15px !important;
}

.m-bottom15 {
    margin-bottom: 15px !important;
}

.mar-left-0 {
    margin-left: 0 !important;
}

.mar-right-0 {
    margin-right: 0 !important;
}

.bg-gray {
    background: #ccc;
}

.pad-right-0 {
    padding-right: 0;
}

.pad-left-0 {
    padding-left: 0;
}

.m-top0 {
    margin-top: 0;
}

.attribute a.expand {
    position: absolute;
    top: 13px;
    right: 39px;
    font-size: 1.2rem;
}

.kpi-table .p-expanded-row-content td {
    background: #ebf8fc;
}

.bg-table-header {
    background: #008c95 !important;
}

.col-white,
label.col-white {
    color: $nep-white-secondary !important;
    text-align: center;
    font-weight: bold;
}

.pad-top15 {
    padding-top: 8px;
    padding-bottom: 0px;
}

.Child-table th .ui-column-title {
    display: block !important;
}

.p-inputswitch {
    position: relative;
    display: inline-block;
    width: 2.6em !important;
    height: 1.35em;
}

.border-right1 {
    border-right: 1px solid $gray-600;
    background: $gray-600;
    width: 1px;
    margin: 4px 0;
}

.card-counter .count-defination {
    position: absolute;
    right: 15px;
    top: 90px;
    font-style: italic;
    opacity: 0.5;
    display: block;
    font-size: 10px;
}

app-linebar-chart {
    width: 100%;
    display: block;
}

app-linebar-chart svg {
    /* max-width: 750px;
	*/
    margin: auto;
}

.no-record-found {
    background: #dcf2fb;
    color: $nep-blue-333;
    text-align: center !important;
    border-color: inherit;
    box-sizing: border-box;
    padding: 0.25em 0.5em;
    border-width: 1px;
    border-style: solid;
    font-size: 0.8rem;
    font-weight: normal;
    border: 1px solid #d5d5d5;
}

.as-of-date {
    font-style: italic;
    opacity: 0.5;
    font-size: 0.7rem;
}


/*--Financials--*/

.chart-area .chart-title h4 {
    color: $nep-dark-thick;
    font-size: 14px;
    font-weight: 600;
    margin: 0;
    padding: 4px 10px;
}

.not-found {
    background: #dcf2fb;
    color: $nep-blue-333;
    text-align: center !important;
    border-color: inherit;
    box-sizing: border-box;
    padding: 0.25em 0.5em;
    border-width: 1px;
    border-style: solid;
    font-size: 0.8rem;
    font-weight: normal;
    border: 1px solid #d5d5d5;
}

.chart-area .chart-title {
    background: $nep-white-secondary;
    border-bottom: 1px solid #c4c4c4;
    overflow: hidden;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
}

.report-edit-dropdown {
    padding: 0 0.8rem;
    background: $nep-white-secondary;
    border-radius: 2px;
    box-shadow: none;
}

.report-edit-adjustment {
    padding: 0 0.8rem;
    background: $nep-white-secondary;
    border-radius: 2px;
    box-shadow: none;
}

.report-edit-dropdown .p-calendar {
    width: 100%;
}

.report-edit-dropdown sub,
.report-edit-dropdown sup {
    font-size: 1rem;
    top: -0.2em;
}

.custom-kpi-table tr th {
    width: 200px;
}

.custom-kpi-table tr td {
    width: 200px;
}

.custom-kpi-table tr td div {
    min-height: 20px;
}

.custom-kpi-table tbody span.ui-column-title {
    display: none;
}

.cashflow-tbl tbody span.ui-column-title {
    display: none;
}

.cashflow-tbl tr th {
    width: 200px;
}

.cashflow-tbl tr td {
    width: 200px;
}

.cashflow-tbl tr td div {
    min-height: 20px;
}

.data-extraction .p-multiselect {
    width: 100%;
}

.data-extraction label.p-multiselect-label.p-corner-all:focus {
    border: 0;
    background: none;
    outline: none;
}

.data-extraction .p-dropdown .p-dropdown-trigger {
    border-right: 0;
    border-top: 0;
    border-bottom: 0;
    cursor: pointer;
    width: 2em !important;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    padding: 0 0.25em;
    border: 1px solid $nep-blue-600;
    color: $nep-white-secondary;
    background: $nep-blue-600;
    transition: background-color 0.2s;
}

.data-extraction .p-dropdown {
    width: 100% !important;
    height: 31px;
    min-width: 100% !important;
}

.data-extraction .input-group p-dropdown {
    width: 100%;
    height: 31px;
}

.kpi-detail .company-name {
    color: $nep-skyblue-light;
    font-size: 1rem;
    font-weight: 600;
}

.p-datatable-emptymessage {
    background: #dcf2fb;
    color: $nep-blue-333;
    text-align: center !important;
    border-color: inherit;
    box-sizing: border-box;
    padding: 0.25em 0.5em;
    border-width: 1px;
    border-style: solid;
    font-size: 0.8rem;
    font-weight: normal;
    border: 1px solid #d5d5d5;
}

.download-report .p-button {
    cursor: pointer;
}

.download-report .p-menu {
    width: auto;
    padding: 0.25em;
    white-space: nowrap;
    background: $nep-white-secondary;
    box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.16) !important;
    border: 0;
}

.download-report .p-menu .p-menu-list .p-menuitem .p-menuitem-link {
    font-size: 12px;
    padding: 0.25em;
    font-weight: 400;
}

.download-report .p-menu .p-menu-list .p-menuitem .p-menuitem-link:hover {
    color: $nep-text-grey;
    text-decoration: none;
    background-color: $gray-800;
    outline: 0;
}

.p-multiselect .p-multiselect-trigger .p-multiselect-trigger-icon:before,
.p-dropdown .p-dropdown-trigger .p-dropdown-trigger-icon::before {
    content: "\e906";
    color: black;
}

#leftContainer>div.row.headerContainer>div.col-7.text-right>p-dropdown>div {
    border-bottom: none;
}

.p-multiselect-panel .p-multiselect-item .p-chkbox {
    display: inline-block;
    vertical-align: middle;
    margin-top: 3px;
    margin-right: 7px;
}

.p-multiselect-panel .p-multiselect-item span {
    display: inline-block;
    vertical-align: middle;
    // margin-top: -5px;
}

.toggle-section .p-accordion .p-accordion-header.p-state-active {
    background: $nep-light-grey;
}

.detail-portfolio-component .portafolio-table h4 {
    color: $nep-dark-thick;
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0rem 0;
    padding: 7.5px;
}

.detail-portfolio-component .portafolio-table h4 span {
    font-weight: 400;
}

.close:not(:disabled):not(.disabled):hover,
.close:not(:disabled):not(.disabled):focus {
    opacity: 0.75;
    outline: none;
}

.w-90 {
    width: 90%;
}

.w-10 {
    width: 10%;
}

.p-calendar {
    width: 100%;
}

td .add-control-btn .btn:hover {
    background: transparent;
    color: $gray-200 !important;
}

.p-editor-toolbar span:nth-child(5) {
    display: none;
}

.chart-section-height {
    min-height: 432px;
}

ul .p-state-highlight {
    background: $gray-800 0% 0% no-repeat padding-box !important;
    box-shadow: 0 0 0 1px $gray-800 inset;
    color: $nep-text-grey !important;
    border: none !important;
    border-color: none !important;
}

.p-state-highlight {
    background: $nep-base-grey !important;
    border: 1px solid $nep-primary;
    border-color: $nep-primary;
    color: $nep-primary !important;
    /* box-shadow: 0 0 0 1px $nep-primary inset; */
}

.p-chkbox-box.p-state-active,
.p-radiobutton-box.p-state-active {
    background: #c0c0c0;
}


/*--loader*/

.loader-dollar {
    align-items: center;
    background: rgba(23, 22, 22, 0.75);
    display: flex;
    height: 100vh;
    justify-content: center;
    left: 0;
    position: fixed;
    top: 0;
    transition: opacity 0.8s linear;
    width: 100%;
    z-index: 999999;
}

.loader-dollar .lds-dual-ring {
    display: inline-block;
    width: 80px;
    height: 80px;
}

.loader-dollar .lds-dual-ring:after {
    content: " ";
    display: block;
    width: 64px;
    height: 64px;
    margin: 8px;
    border-radius: 50%;
    border: 6px solid $nep-text-color-blue;
    border-color: $nep-text-color-blue transparent $nep-light-bg transparent;
}

.add-location:hover {
    background: #009fdf69 !important;
}

.add-location {
    background: $nep-skyblue-light !important;
}

.selected-button {
    background-color: $nep-light-bg !important;
}

.repoListTable {
    margin-top: 12px;
}

@media screen and (max-width: 1024px) {
    .repoListTable {
        width: 778px;
    }
   .esg-model-page .showHandIcon {
        height: 75px !important;
    }
}

@media screen and (min-width: 1280px) and (max-width: 1499px) {
    .repoListTable {
        width: 869px;
    }
}

@media screen and (min-width: 1500px) {
    .repoListTable {
        width: 1094px;
    }
}

.repoListTable table tbody>tr>td {
    border: none !important;
}
.removeTableBorder {
    height: 48px;
}

.tableHeadersStyle {
    font-size: 0.875rem;
    font-weight: 700;
    color: #212121;
}


.fileTypeSize {
    width: 24px;
    height: 24px;
}

.rowDataStyle {
    font-size: 0.875rem;
    color: $nep-text-grey;
    opacity: 1;
}

.rowStyle {
    // border: 1px solid $nep-divider !important;
    height: 2.875rem;
    background: $nep-white-secondary;
}

.UplodedfolderStyle {
    text-align: center !important;
    color: #021b5c;
    font-size: 12px;
    padding: 8px 12px;
    width: 83px;
    height: 32px;
    background: #e5f5fc 0% 0% no-repeat padding-box;
    border-radius: 15px;
    opacity: 1;
}

.restoreButton {
    color: $nep-link !important;
}

.showHandIcon {
    cursor: pointer !important;
    margin-top: 2px;
}

.showHandIcon:hover {
    cursor: pointer !important;
}

.finalFolderStyle {
    text-align: center !important;
    color: #388e3c;
    font-size: 12px;
    padding: 8px 12px;
    width: 83px;
    height: 32px;
    background: #cfffd0 0% 0% no-repeat padding-box;
    border-radius: 16px;
    opacity: 1;
}

.removeTableBorder td {
    border: none !important;
}

.removeTableBorder th {
    background-color: $nep-base-grey !important;
    border: none !important;
    color: $nep-text-grey;
    text-align: left;
}

.p-datatable-tablewrapper>table,
.p-datatable-wrapper>table {
    table-layout: fixed !important;
}

.primeng-custom-style {

    .p-datatable-tablewrapper>table,
    .p-datatable-wrapper>table {
        table-layout: auto !important;
    }
}

td {
    @extend .TextTruncate;
}

.rowStyle:hover {
    background: #E8EAF6 0% 0% no-repeat padding-box !important;
    opacity: 1 !important;
}

.CenterAlign {
    text-align: center !important;
}

.PLForFirstCol {
    padding-left: 1.2% !important;
}

.TextTruncate {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

.mtCell {
    margin-top: 1%;
}

.showToolTip:hover {
    visibility: visible;
    transition: opacity 0.5s ease;
    opacity: 1;
}

.landingPageLoader {
    height: 30px;
    width: 30px;
    margin-top: 25%;
    margin-left: 50%;
}

.loaderpreview {
    height: 30px;
    width: 30px;
    margin-top: 35%;
    margin-left: 50%;
}

.tableLoaderContainer {
    width: inherit;
    border-bottom: 1px solid $nep-divider !important;
    border-left: 1px solid $nep-divider !important;
    border-right: 1px solid $nep-divider !important;
    height: 530px;
}

.docNameHeaderCellWidth {
    width: 18.75rem !important;
}

.textEllipsis {
    display: block;
    overflow: hidden;
    flex: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.uploadedBy {
    margin-left: 6px;
}

.circularOutlined {
    background-color: white !important;
    height: 32px !important;
    border: 1px solid $nep-divider !important;
    border-radius: 20px !important;
    opacity: 1 !important;
    text-align: left;
    letter-spacing: 0px;
    font-size: 14px !important;
    padding: 8px 12px !important;
}

input.mat-chip-input {
    width: 150px;
    margin: 4px;
    flex: none !important;
}

.mat-chip.mat-standard-chip::after {
    background: rgba(255, 255, 255, 0);
}

.mat-chip.mat-standard-chip:not(.mat-chip-disabled) .mat-chip-remove:hover {
    color: $nep-primary;
}

.mat-standard-chip .mat-chip-remove.mat-icon {
    width: 16px !important;
    height: 16px !important;
    padding-right: 12px;
    color: #75787b;
}

.openDocInputText {
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    width: 100% !important;
    border-bottom: 1px solid $gray-600 !important;
    color: $nep-text-grey !important;
    cursor: text !important;
    padding: 0.4vw 0vw 0.5vw 0px !important;
}

.openDocInputText:focus {
    outline: none;
}

.table-deal table {
    border-collapse: collapse;
    width: 100% !important;
    table-layout: fixed;
}

.table-deal.p-datatable .p-sortable-column:not(.p-state-active):hover {
    color: $nep-dark-thick;
}

.disabledDiv {
    top: 235px;
    left: 122px;
    width: 100%;
    height: 30px;
    background: var(--unnamed-color-f0f0f1) 0% 0% no-repeat padding-box;
    background: $gray-800 0% 0% no-repeat padding-box;
    opacity: 1;
}

.disabledText {
    color: $gray-600;
    font-size: 14px;
    padding-left: 8px;
    padding-top: 7px;
}

.disabledDiv:hover {
    cursor: not-allowed;
}

.datatable-container table th {
    width: 200px;
    text-overflow: ellipsis;
}

.datatable-container {
    overflow: auto;
    max-width: 100%;
    background: #FAFAFB 0% 0% no-repeat padding-box;
}

.mat-mdc-menu-content {
    padding: 0px !important;
}

.caret {
    width: 0;
    height: 0;
    display: inline-block;
    border: 5px solid transparent;
    margin-top: 8px;
    margin-left: 8px;
}

.caret.down {
    border-top-color: grey;
}

.divider {
    width: 0px;
    height: 20px;
    border: 1px solid #e7e9ed;
    opacity: 0.96;
    margin-top: 2.4%;
    margin-left: 15px;
}

.mat-mdc-menu-panel {
    min-width: 160px;
    max-width: 280px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    max-height: calc(100vh - 48px);
    border-radius: 4px;
    outline: 0;
}

.disable {
    opacity: 30%;
}

.disable:hover {
    cursor: not-allowed !important;
}

.inlineInput {
    background: #f8f8f8 0% 0% no-repeat padding-box !important;
    border-radius: 4px !important;
    opacity: 1;
    height: 28px !important;
    outline: none !important;
    border: none !important;
    padding-left: 8px !important;
}

.inlineInput:hover {
    outline: none !important;
}

.inlineInput:focus {
    outline: none !important;
}

.hFitContent {
    min-width: 50%;
    max-width: 100%;
    width: -webkit-fill-available;
}

.vFitContent {
    max-height: 100vh;
    min-height: 74vh;
    height: -webkit-fill-available;
}

.auditheader {
    height: 48px !important;
    padding-left: 20px !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
    font-size: 12px !important;
    line-height: 16px !important;
}

.auditcell {
    height: 48px !important;
    font-size: 14px !important;
    line-height: 16px !important;
    padding-left: 20px !important;
    padding-top: 16px !important;
    padding-bottom: 16px !important;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 200px;
}

.mat-form-field-underline {
    position: absolute;
    width: 100%;
    pointer-events: none;
    transform: scaleY(1.0001);
    display: none;
}

.mat-option-text {
    display: inline-block;
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
}

.mat-option {
    height: 36px !important;
}

#tableContainer>p-table>div>div>table {
    border-radius: 4px 4px 0px 0px;
}

.fontStyle14 {
    font-size: 14px !important;
}

.ActionItem {
    color: #021b5c;
    cursor: pointer;
    width: 120px;
}

app-view-pc-aduitlogs>confirm-modal>div>div.nep-card.nep-card-shadow.nep-modal-panel.nep-modal-default {
    width: 600px !important;
    opacity: 1;
}

.recycleBinTable {
    padding: 0.25em 0.5em !important;
}

.noTagsBorder {
    border: 1px solid $nep-divider;
}

.pTags {
    height: 32px;
    border: 1px solid #4061C7;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border-radius: 20px;
    opacity: 1;
    padding: 5px 12px;
    text-align: center;
    margin-top: 8px;
    color: $nep-text-grey;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 8px !important;
}

.pTags:first-child {
    margin-left: 0px !important;
}

.popularTags {
    text-align: left;
    font: normal normal normal 14px/16px "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif;
    letter-spacing: 0.17px;
    color: #212121;
    opacity: 1;
    font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif;
}

@media screen and (max-width: 1024px) {
    .popularTags {
        font-size: 0.99vw;
    }

    .top-left-box {
        width: 76% !important;
    }

    .top-right-box {
        width: 22% !important;
    }
}

@media screen and (min-width: 1280px) {
    .popularTags {
        font-size: 14px;
    }
}

.popularTagsContainer {
    width: 162px;
    max-height: 67%;
    min-height: 25%;
    height: -webkit-fill-available;
}

@media screen and (min-width: 1280px) {
    .notagsImg {
        margin-top: 170px;
        margin-left: 55px;
    }
}

@media screen and (max-width: 1024px) {
    .notagsImg {
        margin-top: 115px;
        margin-left: 40px;
    }
}

@media screen and (max-width: 1024px) {
    .noTagsImgSize {
        width: 40px;
        height: 30px;
    }
}

@media screen and (min-width: 1280px) {
    .noTagsImgSize {
        width: 43px;
        height: 37px;
    }
}

.notags {
    color: $nep-text-grey;
    opacity: 1;
    margin-top: 18px !important
}

@media screen and (max-width: 1024px) {
    .notags {
        font-size: 0.8vw;
    }
}

@media screen and (min-width: 1280px) {
    .notags {
        font-size: 12px;
    }
}

.popularTagsDiv {
    height: 30px;
    width: 162px;
    border-bottom: 1px solid $gray-800;
}

.pt {
    border-bottom: 1px solid $gray-800;
    height: 100%;
}

.tagLoader {
    margin-top: 80%;
    margin-bottom: 100%;
    margin-left: 70px;
    height: 20px;
}

app-view-pc-aduitlogs #tableContainer>p-table>div>div>table>tbody>tr>td {
    padding-left: 0px !important;
}

.Processed {
    display: inline-block;
    background-color: #52be80;
    width: 12px;
    height: 12px;
    border-radius: 10px;
}

.Not_Applicable {
    display: inline-block;
    background-color: $nep-text-color;
    width: 12px;
    height: 12px;
    border-radius: 10px;
}

.In_Process {
    display: inline-block;
    background-color: #f5b041;
    width: 12px;
    height: 12px;
    border-radius: 10px;
}

.Unprocessed {
    display: inline-block;
    background-color: #ec7063;
    width: 12px;
    height: 12px;
    border-radius: 10px;
    margin-top: 0.2rem;
}

.drop_down_item {
    height: 36px;
    padding: 10px 12px;
}

.drop_down_item_label {
    margin-left: 10px;
}

.drop_down {
    height: 28px;
}

.retry {
    letter-spacing: 0px;
    color: $nep-primary;
    font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif;
    text-align: left;
    width: 28px;
    height: 14px;
    padding-left: 16px;
}

#leftContainer>div.row.bodyContainer>div:nth-child(3)>div:nth-child(1)>div>p-calendar>span {
    width: 85% !important;
}

#leftContainer>div.row.headerContainer>div.col-7.text-right>p-dropdown>div>div.p-dropdown-trigger.p-state-default.p-corner-right {
    background: $nep-base-grey 0% 0% no-repeat padding-box;
}

#leftContainer>div.row.headerContainer>div.col-7.text-right>p-dropdown>div>label {
    background: $nep-base-grey 0% 0% no-repeat padding-box;
}

::-webkit-input-placeholder {
    color: $nep-text-color !important;
    opacity: 1;
}

#leftContainer>div.row.headerContainer>div.col-7.text-right>p-dropdown>div>label {
    padding: 0px 2.25em .25em .25em !important;
}

#leftContainer>div.row.bodyContainer>div:nth-child(4)>div:nth-child(1)>div>div>p-autocomplete>span>input {
    padding-left: 0.5rem;
}

.mat-form-field-appearance-legacy .mat-form-field-wrapper {
    padding-bottom: 0 !important;
}

.mat-form-field-appearance-legacy .mat-form-field-infix {
    padding: 0 0;
}

.od_Validation {
    border-top: 1px solid #c62828;
    margin-top: -1px;
    width: 82%;
}

.od_ValidationFull {
    border-top: 1px solid #c62828;
    margin-top: -1px;
    width: 100%;
}

.od_Validation1 {
    border-top: 1px solid #c62828;
    margin-top: -1px;
    width: 91%;
}

.p-calendar .p-datepicker {
    min-width: 60% !important;
}

.KpiInfo {
    width: 12%;
}

.KpiDesc {
    width: 58%;
}

.KpiHeaders {
    padding-left: 20px !important;
    height: 2.875rem;
    padding-right: 20px !important;
}

.KpiValues {
    padding-left: 20px !important;
    height: 2.875rem;
    margin-top: 0 !important;
}

#col\.header:hover {
    background-color: $nep-base-grey !important;
}


.pckpi .p-datatable .p-datatable-tbody>tr {
    /* height: 2.875rem !important; commented for enshi*/
    padding: 0.5rem 1rem;
}

.pckpi .p-datatable .p-datatable-frozen-view {
    border-right: 1px solid #c8c8c8;
}

.p-datatable-scrollable-body {
    overflow-y: scroll !important;
}

.shadowEffect {
    background-clip: padding-box;
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.02), 0 6px 12px rgba(0, 0, 0, 0.175);
}

p-sorticon {
    border: none !important;
    outline: none;
}

.cellPadding {
    padding: 10px 12px !important;
}

.companyText::-webkit-inner-spin-button,
.companyText::-webkit- pin-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0;
}

#KpiListContainer {
    /* width:100%; */
    padding: 20px !important;
    /* margin: -1.5%!important; */
    min-height: 91.2vh !important;
    max-height: 100vh !important;
    background: $nep-base-grey !important;
    margin: -16px -20px;
}

body>app>master>div>div>div>div>cashflow-list>div>div>div>div>div.card-body>p-datatable>div>div.p-datatable-scrollable-wrapper.p-helper-clearfix>div>div.p-datatable-scrollable-body {
    height: 50vh;
    max-height: 75vh;
}

.enableDownload {
    text-decoration: underline;
    cursor: pointer;
}

.disableDownload {
    text-decoration: none;
    cursor: pointer;
}


/* added css for select  and multiselect */

.companyText::-webkit-inner-spin-button,
.companyText::-webkit-outer-spin-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0;
}

.custom-select {
    max-width: 240px;
    background-color: $nep-white !important;
    border: 1px solid $gray-600 !important;
    border-radius: 4px !important;
    opacity: 1;
    cursor: pointer;
    padding-left: 0px !important;
    height: 32px !important;
}

.custom-select>.nep-select-focus {
    /* border: 1px solid $nep-primary !important; */
    border-radius: 4px !important;
}

.custom-select>.nep-select-inner>.nep-select-result>.nep-select-ellipsis {
    text-align: left !important;
    /* letter-spacing: 0px !important; */
    color: #000000 !important;
    opacity: 1 !important;
    font-size: 14px;
}

.custom-select>.nep-select-inner>.nep-select-result>.nep-select-indicator {
    color: $nep-primary !important;
}

.nep-custom-option {
    height: 48px;
    padding: 12px !important;
    text-align: left;
    letter-spacing: 0px;
    color: $nep-text-grey !important;
    opacity: 1;
}

.nep-custom-option:hover {
    background-color: $gray-800 !important;
    cursor: pointer;
}

.autocomplete-container .suggestions-container ul li a {
    height: 48px;
    letter-spacing: var(--unnamed-character-spacing-0);
    text-align: left;
    letter-spacing: 0px;
    color: $nep-text-grey !important;
    opacity: 1;
    font-size: 14px !important;
}

.autocomplete-container .suggestions-container ul li a:hover {
    background: $gray-800 0% 0% no-repeat padding-box !important;
}

.autocomplete-container .suggestions-container ul {
    background: $nep-white 0% 0% no-repeat padding-box !important;
    box-shadow: 1px 3px 12px #00000014 !important;
    border-radius: 4px !important;
    opacity: 1 !important;
}

.autocomplete-container {
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 20%), 0 1px 1px 0 rgb(0 0 0 / 14%), 0 2px 1px -1px rgb(0 0 0 / 12%) !important;
    position: relative;
    overflow: visible;
    height: 30px !important;
    width: 300px;
}

.autocomplete-container .input-container input {
    background: $nep-white 0% 0% no-repeat padding-box !important;
    border: 1px solid $gray-600 !important;
    border-radius: 4px !important;
    opacity: 1 !important;
    height: 32px !important;
    font-size: 14px !important;
}

.autocomplete-container .not-found div {
    border-bottom: none !important;
}

.autocomplete-container input::-webkit-input-placeholder,
.autocomplete-container input:-ms-input-placeholder,
.autocomplete-container input::placeholder {
    /* Edge */
    text-align: left !important;
    letter-spacing: 0px !important;
    color: $nep-text-color !important;
    opacity: 1 !important;
}

.autocomplete-container .input-container .x {
    top: 44% !important;
}

.autocomplete-container .input-container .x i {
    width: 14px !important;
    height: 14px !important;
    color: $nep-icon-grey !important;
    opacity: 1 !important;
    font-size: 18px !important;
}

.autocomplete-container .not-found {
    background: $nep-white 0% 0% no-repeat padding-box !important;
    box-shadow: 1px 3px 12px #00000014 !important;
    border-radius: 4px !important;
    opacity: 1 !important;
}

.autocomplete-container .not-found div {
    letter-spacing: 0px !important;
    color: $nep-text-grey !important;
    opacity: 1 !important;
    font-size: 14px !important;
}

.search-auto {
    width: 300px !important;
    display: inline-block;
    cursor: pointer;
}

.search-auto i {
    position: relative;
    top: -22px;
    float: right;
    font-size: 16px;
    padding-right: 8px;
    color: $nep-icon-grey;
}

.beat-multiselect {
    /** @elaiyaraja-marimuthu can move this style sheet to projects/library folder, if its generic**/
    min-height: 32px;
    background-color: $gray-800;
    border-radius: 16px;
    min-width: 120px !important;
    display: inline-block;
    margin-right: 1rem;
    padding-left: 12px;
    padding-right: 10px;
    padding-top: 6px;
    padding-bottom: 6px;
    font-size: 14px;
    text-align: left;
    letter-spacing: 0px;
    color: $nep-text-grey;
    opacity: 1;
    height: 16px;
}

.multi-close-icon {
    float: right;
    padding-left: 12px;
    padding-right: 0px;
    cursor: pointer;
}

.multi-close-icon i {
    color: $nep-icon-grey;
    padding-top: 1px;
    font-size: 18px;
}

.multi-close-icon i:hover {
    color: $nep-primary;
}

.customselect-div {
    padding: 10px;
    width: 100%;
}

.custom-save-dialog {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    padding-right: 10px !important;
}

.custom-kpi-label {
    padding-bottom: 4px;
    padding-top: 4px;
}

.kpi-dropdown>.p-dropdown {
    border: 1px solid $nep-dark-border !important;
}

#kpi-add-dialog>.nep-modal>.nep-card {
    top: 10% !important;
}

.custom-toast {
    top: 16% !important;
    left: 50% !important;
    transform: translate(-50%, -50%);
    font-size: 14px !important;
}

.custom-toast-confirmModel {
    top: 17.5% !important;
    left: 50% !important;
    transform: translate(-50%, -50%);
    font-size: 14px !important;
}

.custom-toast-addStatusModel {
    top: 34.2% !important;
    left: 50% !important;
    transform: translate(-50%, -50%);
    font-size: 14px !important;
}

#kpi-add-dialog>div>div.nep-card.nep-card-shadow.nep-modal-panel.nep-modal-default>div.nep-card-header.nep-modal-title>p-toast>div {
    width: 63% !important;
    left: 50% !important;
    top: 0 !important;
}

.opKpiTable {
    width: 339px !important;
}

.custom-select-scroll {
    overflow-y: auto !important;
    height: 150px;
}

.custom-nep-input>.nep-input,
.custom-nep-input>.nep-input-textarea,
.custom-kpi-select>.custom-select,
.custom-kpi-select>.custom-select>.nep-select-focus {
    outline: 0;
    border-width: 0 0 2px !important;
    border-bottom: 1px solid #DEDFE0 !important;
    border-radius: 0px !important;
}

.custom-nep-input>input::placeholder,
.custom-nep-input>.nep-input-textarea>textarea::placeholder {
    text-align: left !important;
    letter-spacing: 0.0625rem !important;
    color: $nep-text-color !important;
    opacity: 1 !important;
}

top-holdings-investors .empty-pc{
    background-color: #FFFFFF !important;
}

#no-of-companies.custom-nep-input>.nep-input{
    border: 1px solid #DEDFE0 !important;
    border-radius: 5px !important;
}

#th-no-of-companies-investor.custom-nep-input>.nep-input{
    border: 1px solid #DEDFE0 !important;
    border-radius: 5px !important;
}

portfolio-company{
    .k-grid-content{
      .k-table-row{
        &:hover{
          border:none;
        }
      }
    }
  }

.custom-nep-input>.nep-input>.nep-input-textarea>textarea {
    color: #000000 !important;
}

.custom-kpi-select>.custom-select>.nep-select-focus {
    border-bottom: none !important;
}

#kpi-add-dialog>.nep-modal>.nep-card>.nep-card-body {
    padding: 16px 4px !important;
}

.custom-error-kpi-input>.nep-input {
    border-bottom: 2px solid #C62828 !important;
}


/*end*/


/* KPI Mapping CSS*/

.company-type-list {
    display: inline-block;
    /* border-right: 1px solid #d5d5d5; */
}

.company-info-border {
    border: thin solid #d5d5d5;
}

.company-info-ul>ul {
    margin-bottom: 0px !important;
}

.dot {
    height: 10px;
    width: 10px;
    background-color: green;
    border-radius: 50%;
    display: inline-block;
    margin-top: 10px;
}

.company-active {
    background-color: $gray-800;
}

.custom-kpi-search {
    width: 300px !important;
}

.mat-custom-checkbox .mat-checkbox-frame {
    border: 1px solid var(--input-border-color, $gray-600);
    border-radius: 4px;
}

.mat-custom-checkbox>.mat-checkbox-layout>.mat-checkbox-inner-container {
    width: 20px !important;
    height: 20px !important;
}

.mat-custom-checkbox.mat-checkbox-checked>.mat-checkbox-layout>.mat-checkbox-inner-container>.mat-checkbox-background {
    background-color: $nep-primary !important;
}

.mat-custom-checkbox.mat-checkbox-checked>.mat-checkbox-layout>.mat-checkbox-inner-container>.mat-checkbox-background {
    background-color: $nep-primary !important;
    border-radius: 4px !important;
}

.mat-custom-checkbox:hover .mat-checkbox-frame {
    border: 1px solid $nep-primary !important;
    border-radius: 4px;
}

.mat-custom-checkbox:not(.mat-checkbox-disabled) .mat-checkbox-ripple .mat-ripple-element {
    background-color: $nep-primary !important;
}

.mat-custom-checkbox.mat-checkbox-checked.mat-accent .mat-checkbox-background,
.mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
    background-color: $nep-primary !important;
}

.custom-modal-message {
    font-size: 14px;
    line-height: 24px;
}

#mapping-notfound>.zeroStateKpi {
    border: none !important;
}

#kpi-add-dialog>div>div.nep-card.nep-card-shadow.nep-modal-panel.nep-modal-default>div.nep-card-body>div>div.customselect-div.ng-star-inserted>div:nth-child(2)>typeahead-control>p-autocomplete>span>input {
    padding: 4px 28px 0 16px !important;
}

.company-info-mapping {
    cursor: pointer !important;
}

.mat-tree-node {
    /* background-color: rgb(66, 157, 253); */
    /* color: white; */
    user-select: none;
    cursor: move;
}

.mat-tree-node>.cdk-drag-placeholder {
    opacity: 0;
}


/* items moving away to make room for drop */

.cdk-drop-list-dragging .mat-tree-node:not(.cdk-drag-placeholder) {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}


/* item being dropped */

.cdk-drag-animating {
    transition: transform 200ms cubic-bezier(0, 0, 0.2, 1);
}

.normal-placeholder {
    background: #ccc;
    border: dotted 3px #999;
    min-height: 60px;
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.insert-into-placeholder {
    background: pink;
    border: dotted 3px #999;
    min-height: 60px;
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}


/*End */

.popularTagsDiv {
    height: 30px;
    width: 162px;
    border-bottom: 1px solid $gray-800;
}

.pt {
    border-bottom: 1px solid $gray-800;
    height: 100%;
}

.tagLoader {
    margin-top: 80%;
    margin-bottom: 100%;
    margin-left: 70px;
    height: 20px;
}

app-view-pc-aduitlogs #tableContainer>p-table>div>div>table>tbody>tr>td {
    padding-left: 0px !important;
}

.Processed {
    display: inline-block;
    background-color: #52be80;
    width: 12px;
    height: 12px;
    border-radius: 10px;
}

.Not_Applicable {
    display: inline-block;
    background-color: $nep-text-color;
    width: 12px;
    height: 12px;
    border-radius: 10px;
}

.In_Process {
    display: inline-block;
    background-color: #f5b041;
    width: 12px;
    height: 12px;
    border-radius: 10px;
}

.drop_down_item {
    height: 36px;
    padding: 10px 12px;
    text-align: left !important;
}

.drop_down_item_label {
    margin-left: 10px;
}

.drop_down {
    height: 28px;
}

.retry {
    letter-spacing: 0px;
    color: $nep-primary;
    font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif;
    text-align: left;
    width: 28px;
    height: 14px;
    padding-left: 16px;
}

#leftContainer>div.row.bodyContainer>div:nth-child(3)>div:nth-child(1)>div>p-calendar>span {
    width: 85% !important;
}

#leftContainer>div.row.headerContainer>div.col-7.text-right>p-dropdown>div>div.p-dropdown-trigger.p-state-default.p-corner-right {
    background: $nep-base-grey 0% 0% no-repeat padding-box;
}

#leftContainer>div.row.headerContainer>div.col-7.text-right>p-dropdown>div>label {
    background: $nep-base-grey 0% 0% no-repeat padding-box;
}

::-webkit-input-placeholder {
    color: $nep-text-color !important;
    opacity: 1;
}

#leftContainer>div.row.headerContainer>div.col-7.text-right>p-dropdown>div>label {
    padding: 0px 2.25em .25em .25em !important;
}

#leftContainer>div.row.bodyContainer>div:nth-child(4)>div:nth-child(1)>div>div>p-autocomplete>span>input {
    padding-left: 0.5rem;
}

.mat-form-field-appearance-legacy .mat-form-field-wrapper {
    padding-bottom: 0 !important;
}

.mat-form-field-appearance-legacy .mat-form-field-infix {
    padding: 0 0;
}

.od_Validation {
    border-top: 1px solid #c62828;
    margin-top: -1px;
    width: 95.5%;
}

.od_ValidationFull {
    border-top: 1px solid #c62828;
    margin-top: -1px;
    width: 100%;
}

.od_Validation1 {
    border-top: 1px solid #c62828;
    margin-top: -1px;
    width: 95.5%;
}

.p-calendar .p-datepicker {
    min-width: 60% !important;
}

.KpiInfo {
    width: 12%;
}

.KpiDesc {
    width: 50%;
}

.KpiSector {
    width: 15%;
}

.KpiHeaders {
    padding-left: 20px !important;
    height: 2.875rem;
    padding-right: 20px !important;
    margin-top: 0 !important;
}

#col\.header:hover {
    background-color: $nep-base-grey !important;
}

.ImageStyle {
    margin-top: -13px !important;
    position: absolute;
    padding-left: 18px;
}

.rowStyle:hover .ImageStyle {
    display: block;
}

.ImageStyle:hover {
    display: block;
}

.ImageStyle {
    display: none;
}
.zeroStateKpi {
    background: $nep-white 0% 0% no-repeat padding-box;
    opacity: 1;
    height: 66vh;
    border-radius: 0 !important;
    border: none !important;
    border-top: 1px solid #DEDFE0 !important;
    margin-bottom: 1px;
}

.pckpi .p-datatable .p-datatable-tbody>tr {
    /* height: 2.875rem !important; commented for enshi*/
    padding: 0.5rem 1rem;
}

.pckpi .p-datatable .p-datatable-frozen-view {
    border-right: 1px solid #c8c8c8;
}
.shadowEffect {
    background-clip: padding-box;
    box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.02), 0 6px 12px rgba(0, 0, 0, 0.175);
}

p-sorticon {
    border: none !important;
    outline: none;
}

.companyText::-webkit-inner-spin-button,
.companyText::-webkit-outer-spin-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0;
}

#KpiListContainer {
    /* width:100%; */
    padding: 20px !important;
    /* margin: -1.5%!important; */
    min-height: 91.2vh !important;
    max-height: 100vh !important;
    background: $nep-base-grey !important;
    margin: -16px -20px;
}

body>app>master>div>div>div>div>cashflow-list>div>div>div>div>div.card-body>p-datatable>div>div.p-datatable-scrollable-wrapper.p-helper-clearfix>div>div.p-datatable-scrollable-body {
    height: 50vh;
    max-height: 75vh;
}

.enableDownload {
    text-decoration: underline;
    cursor: pointer;
}

.disableDownload {
    text-decoration: none;
    cursor: pointer;
}


/* added css for select  and multiselect */

.companyText::-webkit-inner-spin-button,
.companyText::-webkit-outer-spin-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0;
}

.custom-select {
    max-width: 240px;
    background-color: $nep-white !important;
    border: 1px solid $gray-600 !important;
    border-radius: 4px !important;
    opacity: 1;
    cursor: pointer;
    padding-left: 0px !important;
    height: 32px !important;
}

.custom-select>.nep-select-focus {
    /* border: 1px solid $nep-primary !important; */
    border-radius: 4px !important;
}

.custom-select>.nep-select-inner>.nep-select-result>.nep-select-ellipsis {
    text-align: left !important;
    /* letter-spacing: 0px !important; */
    color: #000000 !important;
    opacity: 1 !important;
    font-size: 14px;
}

.custom-select>.nep-select-inner>.nep-select-result>.nep-select-indicator {
    color: $nep-primary !important;
}

.nep-custom-option {
    height: 48px;
    opacity: 1;
    padding: 12px 8px !important;
    text-align: left;
    letter-spacing: 0px;
    color: $nep-text-grey !important;
    opacity: 1;
}

.nep-custom-option:hover {
    background-color: $gray-800 !important;
    cursor: pointer;
}

.autocomplete-container .suggestions-container ul li a {
    height: 48px;
    text-align: left;
    letter-spacing: 0px;
    color: $nep-text-grey !important;
    opacity: 1;
    font-size: 14px !important;
}

.autocomplete-container .suggestions-container ul li a:hover {
    background: $gray-800 0% 0% no-repeat padding-box !important;
}

.autocomplete-container .suggestions-container ul {
    background: $nep-white 0% 0% no-repeat padding-box !important;
    box-shadow: 1px 3px 12px #00000014 !important;
    border-radius: 4px !important;
    opacity: 1 !important;
}

.autocomplete-container {
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 20%), 0 1px 1px 0 rgb(0 0 0 / 14%), 0 2px 1px -1px rgb(0 0 0 / 12%) !important;
    position: relative;
    overflow: visible;
    height: 30px !important;
    width: 300px;
}

.autocomplete-container .input-container input {
    background: $nep-white 0% 0% no-repeat padding-box !important;
    border: 1px solid $gray-600 !important;
    border-radius: 4px !important;
    opacity: 1 !important;
    height: 32px !important;
    font-size: 14px !important;
}

.autocomplete-container .not-found div {
    border-bottom: none !important;
}

.autocomplete-container input::-webkit-input-placeholder,
.autocomplete-container input:-ms-input-placeholder,
.autocomplete-container input::placeholder {
    /* Edge */
    text-align: left !important;
    letter-spacing: 0px !important;
    color: $nep-text-color !important;
    opacity: 1 !important;
}

.autocomplete-container .input-container .x {
    top: 44% !important;
}

.autocomplete-container .input-container .x i {
    width: 14px !important;
    height: 14px !important;
    color: $nep-icon-grey !important;
    opacity: 1 !important;
    font-size: 18px !important;
}

.autocomplete-container .not-found {
    background: $nep-white 0% 0% no-repeat padding-box !important;
    box-shadow: 1px 3px 12px #00000014 !important;
    border-radius: 4px !important;
    opacity: 1 !important;
}

.autocomplete-container .not-found div {
    letter-spacing: 0px !important;
    color: $nep-text-grey !important;
    opacity: 1 !important;
    font-size: 14px !important;
}

.search-auto {
    width: 300px !important;
    display: inline-block;
    cursor: pointer;
}

.search-auto i {
    position: relative;
    top: -22px;
    float: right;
    font-size: 16px;
    padding-right: 8px;
    color: $nep-icon-grey;
}

.beat-multiselect {
    /** @elaiyaraja-marimuthu can move this style sheet to projects/library folder, if its generic**/
    min-height: 32px;
    background-color: $gray-800;
    border-radius: 16px;
    min-width: 120px !important;
    display: inline-block;
    margin-right: 1rem;
    padding-left: 12px;
    padding-right: 10px;
    padding-top: 6px;
    padding-bottom: 6px;
    font-size: 14px;
    text-align: left;
    letter-spacing: 0px;
    color: $nep-text-grey;
    opacity: 1;
    height: 16px;
}

.multi-close-icon {
    float: right;
    padding-left: 12px;
    padding-right: 0px;
    cursor: pointer;
}

.multi-close-icon i {
    color: $nep-icon-grey;
    padding-top: 1px;
    font-size: 18px;
}

.multi-close-icon i:hover {
    color: $nep-primary;
}

.customselect-div {
    padding: 10px;
    width: 100%;
}

.custom-save-dialog {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    padding-right: 10px !important;
}

.custom-kpi-label {
    padding-bottom: 4px;
    padding-top: 4px;
}

.kpi-dropdown>.p-dropdown {
    border: 1px solid $nep-dark-border !important;
}

#kpi-add-dialog>.nep-modal>.nep-card {
    top: 10% !important;
}

.custom-toast {
    left: 30px;
    transform: translate(-50%, -50%);
    font-size: 14px !important;
}

#kpi-add-dialog>div>div.nep-card.nep-card-shadow.nep-modal-panel.nep-modal-default>div.nep-card-header.nep-modal-title>p-toast>div {
    width: 63% !important;
    left: 50% !important;
    top: 0 !important;
}

.opKpiTable {
    width: 339px !important;
}

.custom-select-scroll {
    overflow-y: auto !important;
    max-height: 150px;
    height: auto;
}

.custom-nep-input>.nep-input>.nep-input-textarea>textarea {
    color: #000000 !important;
}

.custom-kpi-select>.custom-select>.nep-select-focus {
    border-bottom: none !important;
}

#kpi-add-dialog>.nep-modal>.nep-card>.nep-card-body {
    padding: 16px 4px !important;
}

.custom-error-kpi-input>.nep-input {
    border-bottom: 2px solid #C62828 !important;
}


/*end*/


/* KPI Mapping CSS*/

.company-type-list {
    display: inline-block;
    /* border-right: 1px solid #d5d5d5; */
}

.company-info-ul>ul {
    margin-bottom: 0px !important;
}

.dot {
    height: 10px;
    width: 10px;
    background-color: green;
    border-radius: 50%;
    display: inline-block;
    margin-top: 10px;
}

.company-active {
    background-color: #E8EAF6;
}

.custom-kpi-search {
    width: 300px !important;
}

.mat-custom-checkbox .mat-checkbox-frame {
    border: 1px solid var(--input-border-color, $gray-600);
    border-radius: 4px;
}

.mat-custom-checkbox>.mat-checkbox-layout>.mat-checkbox-inner-container {
    width: 20px !important;
    height: 20px !important;
}

.mat-custom-checkbox.mat-checkbox-checked>.mat-checkbox-layout>.mat-checkbox-inner-container>.mat-checkbox-background {
    background-color: $nep-primary !important;
}

.mat-custom-checkbox.mat-checkbox-checked>.mat-checkbox-layout>.mat-checkbox-inner-container>.mat-checkbox-background {
    background-color: $nep-primary !important;
    border-radius: 4px !important;
}

.mat-custom-checkbox:hover .mat-checkbox-frame {
    border: 1px solid $nep-primary !important;
    border-radius: 4px;
}

.mat-custom-checkbox:not(.mat-checkbox-disabled) .mat-checkbox-ripple .mat-ripple-element {
    background-color: $nep-primary !important;
}

.mat-custom-checkbox.mat-checkbox-checked.mat-accent .mat-checkbox-background,
.mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
    background-color: $nep-primary !important;
}

.custom-modal-message {
    font-size: 14px;
    line-height: 24px;
}

#kpi-add-dialog>div>div.nep-card.nep-card-shadow.nep-modal-panel.nep-modal-default>div.nep-card-body>div>div.customselect-div.ng-star-inserted>div:nth-child(2)>typeahead-control>p-autocomplete>span>input {
    padding: 4px 28px 0 16px !important;
}

#kpi-add-dialog>div>div.nep-card.nep-card-shadow.nep-modal-panel.nep-modal-default>div.nep-card-body>div>div.customselect-div.ng-star-inserted>div:nth-child(2)>typeahead-control>p-autocomplete>span>button>span.p-button-icon-left.p-clickable.pi.pi-caret-down {
    color: #000000 !important
}

.custom-kpi-search>input:focus,
.info-search>input:focus {
    outline-style: none !important;
    outline: none !important;
    outline: 0 !important;
    border: 1px solid $nep-primary !important;
}

.mapping-divider {
    border-bottom: 1px solid $gray-600;
}

.mat-tree-node:hover {
    background: #F5F6FB 0% 0% no-repeat padding-box;
}

.mat-tree-node {
    border-bottom: 1px solid $nep-divider;
}

.mat-icon-button {
    padding-top: 6px !important;
    vertical-align: unset !important;
}

.mat-button .mat-button-wrapper>*,
.mat-icon-button .mat-button-wrapper>* {
    vertical-align: unset !important;
}

.drop-above {
    width: 100%;
}

.drop-below {
    width: 100%;
}

.drop-center {
    width: 100%;
}


/*End */

.info-search>input {
    border: 1px solid #ced4da !important;
    height: 40px;
}

.user-table .p-datatable .p-datatable-thead>tr>th:first-child {
    text-align: left;
    height: 2.5rem;
    width: 20%;
    /* padding-left: 20px!important; */
}

.user-table .p-datatable .p-datatable-thead>tr>th {
    text-align: center;
    height: 2.5rem;
    /* padding-left: 20px!important; */
}

.user-table .p-datatable .p-datatable-tbody>tr>td:first-child {
    text-align: left !important;
    height: 2.75rem;
}

.user-table .p-datatable .p-datatable-tbody>tr>td {
    text-align: center !important;
    height: 2.75rem;
    background-color: $nep-white-secondary;
}

.feature-width {
    width: 50% !important;
}

.action-width {
    width: 20% !important;
}

.mat-checkbox-disabled {
    cursor: default;
    opacity: 30%;
}

.company-info-mapping:hover {
    border-right: 1px solid #D5D5D5;
    background-color: #F7F8FC;
}

.company-info-mapping:last-child {
    border-bottom: none !important;
}

.company-tabs>.nep-tabs>.nep-tabs-header>.nep-tabs-header-tabs>.nep-tabs-inner>.nep-tabs-scroll>.nep-tabs-tab {
    color: $nep-text-grey;
    font-size: 0.9rem !important;
}

.mat-custom-checkbox>label {
    margin-bottom: 0px !important;
}

.mat-checkbox-disabled>.mat-checkbox-layout>.mat-checkbox-inner-container>.mat-checkbox-background {
    background-color: $gray-800 !important;
    border: 1px solid $nep-text-color !important;
    border-radius: 4px !important;
    box-sizing: border-box !important;
}

.user-ptable>.p-datatable>.p-datatable-scrollable-wrapper {
    border-bottom: none !important;
}


/*End */


/* Added code for Angular 11 */

.mat-icon-button {
    outline: none !important;
}

.pi-sort-alt:before {
    content: '\f0dc';
}

.pi-sort-amount-up-alt:before {
    content: '\f0de';
}

.pi-sort-amount-down:before {
    content: '\f0dd';
}


/*End */

.p-sortable-column-badge {
    display: none !important;
}

.contain {
    object-fit: contain;
}


/*End */

.cards2 {
    overflow-x: scroll !important;
    white-space: nowrap !important;
    width: 1254px !important;
    padding: 1rem;
}

.cards {
    overflow-x: scroll !important;
    white-space: nowrap !important;
    width: 503px !important;
    padding: 1rem;
}

.img-card {
    border-radius: 4px !important;
    display: inline-block !important;
}

.modofchange {
    margin-top: 16px;
}

.description {
    display: none;
}

.rowStyle:hover .description {
    display: block;
}

.description:hover {
    display: block;
}

.KpiDescValues {
    width: 94%;
}

.fund-select-dropdown {
    background-color: $nep-skyblue !important;
}

.lpreport-select {
    background-color: $nep-base-grey !important;
    float: right;
}

.lpconfig>p-table>.p-datatable .p-datatable-thead>tr>th,
.lpconfig>p-table>.p-datatable .p-datatable-thead>tr>th:hover {
    background-color: $nep-white-secondary !important;
    border-color: $gray-600;
    border-top: none !important;
}

.lpconfig>p-table>.p-datatable .p-datatable-thead>tr>th:first-child,
.lpconfig>p-table>.p-datatable .p-datatable-thead>tr>th:hover:first-child {
    border-right: none !important;
}

.companyStatus {
    text-align: center !important;
    color: #c68700;
    font-size: 14px;
    padding: 8px 12px;
    width: 83px;
    height: 32px;
    background: #ffecb3 0% 0% no-repeat padding-box;
    border-radius: 15px;
    opacity: 1;
}

.hSpace {
    width: 27%;
}

.hSpace1 {
    width: 38%;
}

.hColor {
    color: $nep-text-grey;
}

.vColor {
    color: #000000;
}

.companyLogoStyle {
    height: 100%;
    width: 100%;
    padding-bottom: 12px;
    padding-top: 12px;
    object-fit: contain;
}

.linkStyle {
    color: $nep-primary;
}

.uploadCompanyLogo {
    cursor: pointer;
    border-radius: 4px;
    padding: 3px 11px 3px 11px !important;
    // width: 230px !important;
    width: fit-content;
    border-color: $nep-button-primary !important;
}

.browsetext {
    position: relative;
    top: 1px;
    color: $nep-button-primary;
}

.broseicon {
    padding-right: 8px;
    padding-top: 2px;
}

.rowpadding {
    padding-top: 2px;
}

.hidefile {
    display: none !important;
}

.border-radius-common {
    border-radius: 4px !important;
}

.updateinfo-btn-cancel {
    padding-right: 6px !important;
}

.updateinfo-btn-update {
    padding-left: 6px !important;
}

.uploadLogoIcon {
    padding: 0px !important;
}


/*End */

.lp-config-table>.p-datatable>.p-datatable-scrollable-wrapper>.p-datatable-frozen-view>.p-datatable-scrollable-body {
    background-color: $nep-white-secondary;
}

.lpconfig-select>.custom-select>.nep-select-inner>.nep-select-result {
    padding: 4px 28px 4px 8px !important;
}

.lp-config-table>.p-datatable>.p-datatable-scrollable-wrapper>.p-datatable-unfrozen-view>.p-datatable-scrollable-body>table>tbody>tr>td:first-child,
.lp-config-table>.p-datatable>.p-datatable-scrollable-wrapper>.p-datatable-unfrozen-view>.p-datatable-scrollable-header-box>table>thead>tr>th:first-child {
    border-left: none !important;
}

.lp-config-table>.p-datatable>.p-datatable-scrollable-wrapper>.p-datatable-unfrozen-view>.p-datatable-scrollable-header>.p-datatable-scrollable-header-box>table>thead>tr>th:first-child {
    border-left: none !important;
}

.lp-config-table>.p-datatable>.p-datatable-scrollable-wrapper>.p-datatable-scrollable-view>.p-datatable-scrollable-body>table>tbody>tr>td:first-child,
.lp-config-table>.p-datatable>.p-datatable-scrollable-wrapper>.p-datatable-scrollable-view>.p-datatable-scrollable-header>.p-datatable-scrollable-header-box>table>thead>tr>th:first-child {
    border-left: none !important;
}

.dcoumentcircle {
    border: 1px solid $nep-white-secondary;
    display: inline-block;
    position: absolute;
    width: 2.5em;
    height: 2.5em;
    text-align: center;
    border-radius: 50%;
    vertical-align: middle;
    color: white;
    @extend .Body-R;
    line-height: 2rem !important;
}
.background-circle-bg0{
    background: linear-gradient(180deg, #021155 0%, #9C27B0 100%);
}
.background-circle-bg1{
    background: linear-gradient(270deg, #EDCF28 0%, #DEAD09 100%);

}
.background-circle-bg2{
    background: linear-gradient(90deg, #67CCD9 0%, #A9C97F 100%);

}
.background-circle-bg3{
    background: linear-gradient(90deg, #2F85E9 0%, #65CBD9 100%);
}
.background-circle-bg4{
    background: linear-gradient(90deg, #EB7600 0%, #F8BE63 100%);
}
.background-circle-bg5{
    background: linear-gradient(90deg, #4D4D4D 0%, #666666 100%);
}
.circles {
    text-align: left;
    float: left;
    width: 55px;
    height: 36px;
    position: relative;
}

.circles2 {
    text-align: left;
    float: left;
    width: 34px;
    height: 36px;
    position: relative;
}

.moreplus {
    font-size: 1em;
    float: left;
    margin-top: 7px;
}

.assigneeswidth {
    width: 130px;
}

.dcoumentcircle:nth-child(2) {
    margin-left: 16px;
}
.dcoumentcircle:nth-child(3) {
    margin-left: 32px;
}
.dcoumentcircle:nth-child(4) {
    margin-left: 48px;
}

.dcoumentcircle:nth-child(5) {
    margin-left: 64px;
}
.dcoumentcircle:nth-child(6) {
    margin-left: 80px;
}

.p-doc-dropdown>.p-dropdown {
    background: transparent !important;
    border: none !important;
}

.form-control:focus {
    border-color: #ced4da !important;
}

.cashflow-expand-button {
    background-color: transparent !important;
}

td>.p-button:enabled:hover {
    border: none !important;
}

.cashflow-expand-button.p-button-icon-only {
    color: $nep-text-grey !important;
    border-bottom: none !important;
}

.p-customselect>.p-selectbutton .p-button {
    border: 1px solid $nep-dark-border;
    background: $nep-white;
    font-weight: normal;
    color: $nep-darker-blue-color;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
    padding: .25em 1em .25em 2.1em !important;
}

.p-customselect>.p-selectbutton .p-button:not(.p-disabled):not(.p-highlight):hover {
    background: $gray-600 !important;
    color: $nep-white !important;
}

.p-customselect>.p-selectbutton .p-button.p-highlight {
    background: $nep-dark-highlight;
    border-color: $nep-dark-border;
    color: $nep-white;
}

.p-customselect>.p-selectbutton .p-button.p-highlight:hover {
    background: $nep-dark-highlight;
    color: $nep-white;
}

.tab-margin-top {
    margin-top: -5px !important;
}

.custom-tabs>.nep-tabs-line .nep-tabs-active:after {
    height: 3px !important;
}

.financial-tabs>.nep-tabs>.nep-tabs-panel {
    position: initial !important;
}

.custom-kpi-select>.custom-select {
    width: 100%;
    max-width: 100% !important;
}

.kpi-set-bgcolor {
    background-color: #ffc7ce !important;
    cursor: pointer;
}

.kpi-set-calc-bgcolor {
    background-color: #D2EDFD !important;
}

.kpi-unset-bgcolor {
    background-color: $nep-white-secondary;
    cursor: '';
}

.p-datatable-scrollable-body>table>.p-datatable-tbody>tr:first-child>td,
.p-datatable .p-datatable-tbody>tr:first-child>td {
    border-top: 0 none !important;
}

.toggle-section .p-accordion .p-accordion-header .p-accordion-tab-active {
    background: $nep-light-grey;
}

.repository-autocomplete {
    .p-autocomplete {
        input {
            border-radius: 4px !important;
        }
    }
}

.rowStyle>:nth-child(2) {
    cursor: pointer !important;
}

p-sorticon>.pi-fw {
    width: 1.28571429em !important;
    text-align: center !important;
}

p-sorticon>.pi-fw {
    display: inline-block !important;
    font: normal normal normal 14px/1 FontAwesome, Arial, Verdana, Tahoma, sans-serif !important;
    font-size: inherit !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

.p-autocomplete-dd .p-autocomplete-dropdown {
    height: 30px;
}

.p-dropdown-label {
    padding: 3px;
}

.p-dropdown-trigger,
.p-autocomplete-dropdown,
.p-multiselect-trigger {
    align-items: inherit !important;
    padding-top: 4px;

    .pi {
        display: inline-block;
        font: normal normal normal 14px/1 FontAwesome;
        font-size: 20px;
        text-rendering: auto;
        -webkit-font-smoothing: antialiased;
    }

    .pi-chevron-down:before {
        content: "\f107" !important;
        color: $nep-icon-grey !important;
    }
}

.showFinancialNote {
    position: relative;
    top: 16px;
}

.showNoteIcon {
    position: relative;
    top: -2px;
}

.p20 {
    padding: 20px;
}

.p-custom-dropdown>.p-dropdown {
    width: 100%;
    padding-top: 3px;
}

.p-calendar>.p-button.p-button-icon-only {
    height: 1.85rem;
}

button:focus,
.angular-editor-textarea {
    outline: none !important;
}

.mat-mdc-menu-panel {
    margin-top: 5px;

    .mat-mdc-menu-content {
        color: $nep-text-color-dark;

        .mat-mdc-menu-item {
            .nav-link {
                font-size: 1rem;

                a {
                    color: $nep-text-color-dark !important;
                }
            }
        }

        .mat-mdc-menu-item:first-child {
            border-bottom: 1px solid $nep-divider;
        }
    }
}

.p-autocomplete-input,
.p-calendar>.p-inputtext {
    border-radius: inherit !important;
}

.p-inputswitch {
    top: 3px !important;
}

.stock-input>input {
    margin-top: -2px !important;
}

.status-input>typeahead-control>p-autocomplete>.p-autocomplete {
    margin-top: 5px !important;
}

.p-radiobutton-label {
    margin-bottom: 0px !important;
    padding-bottom: 1px !important;
}

.financialsEmptyTable {
    padding-left: 43%;
}

.repository-calendar {
    .p-calendar {
        .p-button-icon-only {
            height: 2rem !important;
        }

        .p-inputtext {
            color: #000000 !important
        }
    }
}

.table-header-calender-width>.p-calendar,
.tablefilter-dropdrown-width>.p-dropdown {
    width: 260px;
    border: 1px solid $nep-light-border !important;
    border-radius: 4px;
    height: 32px;
    padding-top: 4px;
    font-size: 0.75rem;
    padding-left: 14px !important;
}

.ui-inputswitch1.p-inputswitch {
    padding-top: 4px !important;
    padding-bottom: 5px !important;
    width: 31px;
    height: 14px;
}

.ui-inputswitch2.p-inputswitch {
    padding-top: 5px !important;
    padding-bottom: 5px !important;
    width: 30px;
    height: 14px;
}

.ui-inputswitch1>.p-inputswitch-slider:before {
    width: 12px !important;
    height: 12px !important;
}

.ui-inputswitch2>.p-inputswitch-slider:before {
    width: 12px !important;
    height: 12px !important;
}

.ui-inputswitch1>.p-inputswitch-slider,
.ui-inputswitch2>.p-inputswitch-slider {
    width: 30px !important;
    height: 14px !important;
}

.fasearchicon {
    position: absolute !important;
    top: 8px !important;
    right: 15px !important;
    font-size: 15px !important;
    color: $nep-primary !important;
    width: 16px !important;
    height: 16px !important;
}

.header-right>.notification-bell>.p-ink,
.header-right>.notification-bell>.p-ink:hover {
    background: $nep-orange;
    cursor: pointer !important;
}

.notification-sidebar {
    width: 360px;
    height: 100vh;
    position: fixed;
    top: 3.8rem !important;
    right: 0;
    background: $nep-white 0% 0% no-repeat padding-box;
    box-shadow: 0px 6px 12px $nep-shadow;
    border-radius: 4px;
    overflow-y: auto;
    border: none !important;
    z-index:999;
    .sidebar-header {
        border-bottom: 1px solid $nep-divider;
        color: $nep-text-grey;
        text-align: left;
        font-size: 16px;
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }

    .circle-user {
        width: 32px;
        height: 32px;
        color: $nep-white;
        background: $nep-primary 0% 0% no-repeat padding-box;
        border-radius: 50%;
        letter-spacing: 0px;
        padding: 6px;
    }

    .sidebar-content-title {
        letter-spacing: 0px;
        color: $nep-primary;
        width: 96%;
        display: inline-block;
    }

    .sidebar-desc {
        letter-spacing: 0px;
        color: $nep-text-grey;
    }

    .document-title {
        font-size: 14px;

        a {
            color: $nep-primary;

            &:hover {
                color: $nep-dark-highlight;
                text-decoration: underline;
            }
        }

        .expand-doc {
            max-width: 250px;
        }

        .doc-date {
            color: $gray-600;
            opacity: 1;
            font-size: 14px;
        }
    }

    .doc-border {
        border-bottom: 1px solid $nep-light-border;
    }
}

.p-sidebar-mask.p-component-overlay {
    background: transparent !important;
    z-index: 0 !important;
}

.fixed-menu.mat-mdc-menu-panel {
    height: 328px !important;
    min-width: 160px !important;
    max-width: 292px !important;
    overflow: none !important;
    overflow-y: none !important;
    overflow-x: none !important;
    -webkit-overflow-scrolling: none !important;
    max-height: none !important;
    border-radius: 4px;
    outline: 0;
}

.nep-tabs-custom .nep-tabs-active {
    z-index: 100;
    cursor: default;
    border-bottom: none !important;
    border: 1px solid #dee2e6;
    border-radius: 4px 4px 0 0;
    margin-top: none !important;
    background: $nep-base-grey;
}

.nep-tab-alignment,
.nep-tab-alignment2 {
    margin-left: 0px;
    width: 100%;
}

.nep-tab-alignment {
    margin-top: -5px;
}

.nep-tabs-custom .nep-tabs-active:after {
    position: absolute;
    width: 100%;
    height: 2px;
    background: transparent !important;
    background: var(--primary-color, transparent !important);
    content: " ";
}

.calender-font {
    font-size: 0.75rem !important;
}

.calender-font>.p-calendar>.p-datepicker {
    top: unset !important;
}

.table-header-calender-width>.p-calendar>.p-button.p-button-icon-only {
    height: 1.5rem !important;
    border-bottom: none !important;
}

.table-header-calender-width .p-inputtext {
    border-bottom: none !important;
}

.nep-tab-alignment-subtab>.nep-tabs-tab {
    padding-left: 14px !important;
    padding-right: 14px !important;
}

.financial-page>.financial-tabs>.nep-tabs>.nep-tabs-header>.nep-tabs-header-tabs {
    padding-left: 1rem !important;
}

.custom-kpi-table>.p-datatable .p-datatable-thead>tr>th,
.subfeature-tbl>.p-datatable .p-datatable-thead>tr>th {
    border-left: none !important;
}

.custom-kpi-table>.p-datatable .p-datatable-tbody>tr>td:first-child,
.subfeature-tbl>.p-datatable .p-datatable-tbody>tr>td:first-child {
    border-left: none !important;
}

.custom-kpi-table>.p-datatable .p-datatable-tbody>tr:last-child>td {
    border-bottom: none !important;
}

.nep-tabs-custom .nep-tabs-active {
    font-family: "Helvetica Neue LT W05_65 Medium", Arial, Verdana, Tahoma, sans-serif;
    color: $nep-primary !important;
}

.financial-tabs>.nep-tabs-line .nep-tabs-active:after {
    height: 1px !important;
}

.custom-tabs>.nep-tabs-line .nep-tabs-active {
    font-family: "Helvetica Neue LT W05_65 Medium", Arial, Verdana, Tahoma, sans-serif;
    color: #4061C7 !important;
}

.p-sidebar-right {
    width: 360px !important;
}

.expand-doc:hover {
    text-decoration: none !important;
}

.BusinessDescriptionlabel {
    font: normal normal medium 14px/17px "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif;
    letter-spacing: 0px;
    color: $nep-black;
}

.BusinessDescription-css.form-control {
    border: none !important;
}

.flexmoduleContainer {
    background: $nep-white 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000014;
    border: 1px solid $nep-divider;
    border-radius: 4px;
    opacity: 1;
    padding: 16px !important;
}

.flexmoduleContainer.pr-0 {

    padding-right: 0px !important;
}

.panel .panel-heading .nav-tabs {
    margin-bottom: -11px;
}

.panel-default {
    border-color: $nep-panel-border;
}

.panel {
    background-color: $nep-white-secondary;
    border: 1px solid transparent;
    border-radius: 4px;
}

.panel-default>.panel-heading {
    color: $nep-panel-color;
    border-color: $nep-panel-border
}

.panel-heading {
    padding: 10px 15px;
    padding-top: 0px;
    border-bottom: 1px solid transparent;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
}

.custom-tabs>.nav-tabs .nav-link.active,
.custom-tabs>.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
    color: $nep-primary;
    background-color: $nep-white;
    border-color: $nep-nav-tab-border-color $nep-nav-tab-border-color $nep-white-secondary; //border-bottom: #FAFAFB 4px solid;
    // top: 2px !important;
    position: relative !important;
    @extend .Body-R;
}

.nav-tabs .nav-pc.active,
.nav-tabs .nav-item.show .nav-pc {
    z-index: 999 !important;
}

.nav-tabs .nav-link {
    border: 1px solid #f3000000;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    background: $gray-800;
    color: #666666;
    position: relative;
    top: 1px;
}

.card-body .formcss {
    margin-bottom: 0rem !important
}

.addportfolio>.card-body>.add-update-formcss {
    padding: none !important;
    margin-bottom: none !important;
    margin-top: none !important
}

.addportfolio>.card-body>form {
    padding: none !important;
    margin-top: none !important
}

.geography-clear {
    color: $nep-primary;
}

.addportfolio {
    height: 495px;
    overflow-y: scroll;
    padding-right: 20px;
    padding-left: 20px;
}

.geographicLocations>.p-datatable .p-datatable-tbody>tr:last-child>td {
    border-bottom: none !important;
}

.geographicLocations>.p-datatable .p-datatable-tbody>tr>td:first-child {
    border-left: none !important;
}

.geographicLocations>.p-datatable .p-datatable-thead>tr>th {
    border-left: none !important;
}

.geographicLocations>.p-datatable .p-datatable-tbody>tr>.table-data-right:last-child {
    border-right: none !important;
}

.portfolio-detail-component {
    typeahead-control {
        p-autocomplete {
            .p-autocomplete {
                width: 280px !important;
                background: $nep-white 0% 0% no-repeat padding-box !important;
                border: 1px solid #CAC9C7 !important;
                border-radius: 4px 4px 4px 4px !important;
                opacity: 1;

                button {
                    border-bottom: none !important;
                    padding-top: 5px;
                    border-radius: 0px 4px 4px 0px !important;
                }

                input {
                    border-bottom: none !important;
                    padding-left: 16px !important;
                    padding-bottom: 9px;
                    padding-top: 9px;
                }
            }
        }
    }

    .chart-section-height {
        min-height: auto !important;
    }
}

.location-select {
    typeahead-control {
        p-autocomplete {
            .p-autocomplete {
                input {
                    padding-left: 12px !important;
                }
            }
        }
    }
}

.typeheader-margin {
    p-autocomplete {
        span {
            margin-top: 5px !important;
        }
    }
}

.header-cntrl {
    p-autocomplete {
        .p-autocomplete {
            input {
                padding-left: 12px !important;
            }
        }
    }
}

.tabradius>.card-body-form-form {
    padding: 0 !important;
    margin-bottom: 0 !important
}

.locationcntrl-padding-left-lbl {
    padding-left: 0.75rem;
}

.locationcntrl-padding-left-lbl.required-field:after {
    padding-left: 2px !important;
    content: "*";
    color: red !important;

}

.headerfontsize {
    font-size: 0.75rem;
}

.leftrem {
    padding-left: 0.75rem;
}

input:focus {
    outline: none !important;
}

.cloud_download {
    padding: 4px 12px;
}
.esg-tbl .cloud_download {
    padding: 2px 8px;
}
.textsplit {
    border-right: 1px solid $gray-600;
}

.search-text-company {
    border-top: none !important;
    border-bottom: none !important;
    border-right: 1px solid $nep-divider;
    border-left: 1px solid $nep-divider;
    width: 343px;
    height: 31px;
    background: $nep-white !important;
}

.search-text-groups {
    border-top: none !important;
    border-bottom: none !important;
    border-right: none !important;
    border-left: 1px solid $nep-divider;
    width: 343px;
    height: 41px;
    background: $nep-white;
}

.search-groups {
    border: none !important;
    width: 100% !important;
    padding: 20px 12px;
    height: 42px !important;
    background: $nep-white;
    color: $nep-dark-black !important;
}

.cursor-filter {
    cursor: pointer;
}

.table-search {
    margin-right: 9px;
}

.filter-bg {
    background: $nep-white 0% 0% no-repeat padding-box;
}

.kpis-custom-select {
    .custom-select {
        .nep-select-inner {
            .nep-select-result {
                padding-left: 12px !important;
            }
        }
    }
}

#kpi-add-dialog.AddOrUpdateKpi>.nep-modal>.nep-card>.nep-card-body {
    padding: 0px !important;
}

.data-analytics-filter-model {
    .nep-modal {
        .nep-card {
            top: 50% !important;
        }
    }
}

.AddOrUpdateKpi {
    .nep-modal {
        .nep-card {
            width: 400px !important;
        }
    }
}

.tabradius>.card-body-form-form {
    padding: 0 !important;
    margin-bottom: 0 !important
}

.locationcntrl-padding-left-lbl {
    padding-left: 0.75rem;
}

.default-txtcolor>.nep-input {
    color: $nep-dark-black !important;
}

.kpis-custom-select {
    .custom-select {
        .nep-select-inner {
            .nep-select-result {
                padding-left: 12px !important;
            }
        }
    }
}

.table-pref {
    padding-left: 0.75rem !important;
}

.toggle-switch>.p-inputswitch>.p-inputswitch-slider {
    width: 33px !important;
    height: 16px !important;
}

.toggle-switch>.p-inputswitch .p-inputswitch-slider:before {
    margin-top: -0.35rem !important;
    left: 0.12em !important;
}

.mat-menu-dropdown {
    width: 260px !important;
    min-width: 260px !important;
}

.mat-menu-calendar {
    margin-left: -10px !important;
    width: 260px !important;
    min-width: 260px !important;
}

.fx-rates-table-body {
    .p-datatable {
        .p-datatable-scrollable-wrapper {
            border-right: none !important;
            border-bottom: none !important;
            border-bottom-left-radius: 4px !important;
            border-bottom-right-radius: 4px !important;
        }
    }
}

.pc-tabs>.nav-tabs .nav-link.active,
.pc-tabs>.nav-tabs .nav-item.show .nav-link {
    border-bottom: $nep-white 4px solid !important;
}

.mat-mdc-menu-panel .mat-mdc-menu-content .mat-mdc-menu-item .pc-menu {
    font-size: 14px !important;

    .fa {
        font-size: 16px !important;
        color: $nep-green !important;
    }
}

.fx-rates-table-body .p-datatable .p-datatable-frozen-view {
    border-right: 1px solid $gray-600;
}

.fx-rates-table-body .p-datatable .p-datatable-frozen-view {
    border-right: 1px solid $gray-600;
}

.fx-rates-table-body>.p-datatable>.p-datatable-scrollable-wrapper>.p-datatable-unfrozen-view>.p-datatable-scrollable-body>table>tbody>tr>td:first-child,
.fx-rates-table-body>.p-datatable>.p-datatable-scrollable-wrapper>.p-datatable-unfrozen-view>.p-datatable-scrollable-header-box>table>thead>tr>th:first-child {
    border-left: none !important;
}

.fx-rates-table-body>.p-datatable>.p-datatable-scrollable-wrapper>.p-datatable-unfrozen-view>.p-datatable-scrollable-header>.p-datatable-scrollable-header-box>table>thead>tr>th:first-child {
    border-left: none !important;
}

.fx-rates-table-body>.p-datatable>.p-datatable-scrollable-wrapper>.p-datatable-scrollable-view>.p-datatable-scrollable-body>table>tbody>tr>td:first-child,
.fx-rates-table-body>.p-datatable>.p-datatable-scrollable-wrapper>.p-datatable-scrollable-view>.p-datatable-scrollable-header>.p-datatable-scrollable-header-box>table>thead>tr>th:first-child {
    border-left: none !important;
}

.fxrate-opacity {
    li {
        a {
            img {
                opacity: 70% !important;
            }
        }
    }
}

.workflow-draft {
    .mat-accordion {
        .mat-expansion-panel {
            .mat-expansion-panel-header {
                .mat-expansion-panel-header-title {
                    letter-spacing: 0px;
                    color: $nep-black !important;
                    font-size: 14px;
                }

                background: $nep-base-grey 0% 0% no-repeat padding-box !important;
                padding-left: 20px;
                padding-right: 20px;
                border-bottom:1px solid $nep-light-border;
            }
        }

        .mat-action-row {
            padding: 8px 20px !important;
            background: $nep-base-grey 0% 0% no-repeat padding-box;
            border-bottom: 1px solid $nep-divider !important;
            border-top: 1px solid $nep-divider !important;

            button[disabled] {
                position: relative !important;
                box-shadow: none !important;
                cursor: not-allowed !important;
            }

            button {
                border: 1px solid $nep-primary;
                border-radius: 4px;
                color: $nep-primary !important;
                font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;

                &:focus,
                &:hover {
                    border-color: $nep-primary !important;
                }
            }

            .btn-nep-default {
                background: #FFFFFF 0% 0% no-repeat padding-box;
            }
        }

        .mat-expansion-panel:not([class*=mat-elevation-z]) {
            box-shadow: 0px 3px 6px $nep-light-border !important;
        }

        .draft-content {
            padding-top: 20px;
            padding-right: 20px;
            padding-left: 10px;
            padding-bottom: 0px;

            .title {
                padding-bottom: 20px;
                color: $nep-text-grey;
                font-weight: normal !important;
                font-size: 14px !important;
            }

            .checkbox-section {
                padding-right: 20px;
                padding-bottom: 8px;
                width: 250px;

                .checkbox-title {
                    text-align: left;
                    font-size: 14px !important;
                    letter-spacing: 0px;
                    color: $nep-dark-black;
                }
            }
        }
    }

    .mat-expansion-panel-header.mat-expanded {
        height: 48px !important;
    }

    .mat-expansion-panel-header-description {
        .row {
            width: 100%;
            margin-left: 0px;
        }
    }

    .mat-expansion-panel-header-description,
    .mat-expansion-indicator::after {
        color: #75787B;
    }
}

.drag-dropdown {
    .custom-select {
        width: 296px !important;

        .nep-list {
            .nep-scroll {
                .custom-select-scroll {
                    max-height: 200px !important;

                    .nep-custom-option {
                        height: 40px !important;
                    }
                }
            }
        }

        .nep-select-inner {
            .nep-select-result {
                .nep-select-ellipsis {
                    color: #212121 !important;
                    opacity: 1;
                    font-size: 14px;
                }
            }
        }
    }
}

@keyframes spinner {
    to {
        transform: rotate(360deg);
    }
}

.fa-circle-o-notch {
    color: $nep-primary !important;
    animation: spinner 1s linear infinite;
}

.padding-radio-save,
.padding-radio {
    p-radiobutton {
        .p-radiobutton {
            .p-highlight {
                border-color: $nep-button-primary !important;
                background: none !important;

                .p-radiobutton-icon {
                    background-color: $nep-button-primary;
                }
            }
        }

        .p-radiobutton .p-radiobutton-box.p-highlight:not(.p-disabled):hover {
            border-color: none !important;
            background: none !important;
        }
    }
}

.lpborder-app-select {
    .custom-select {
        border: 1px solid #4061C7 !important;

        .nep-select-inner {
            .nep-select-result {
                .pi {
                    color: #4061C7 !important;
                }
            }
        }
    }
}

.lpTemplateConfig-app-select {
    .custom-select {
        min-width: 240px !important;

        .nep-list {
            .nep-scroll {
                .custom-select-scroll {
                    max-height: 200px !important;
                }
            }
        }

        .nep-select-inner {
            .nep-select-result {
                padding-top: 4px !important;
                padding-bottom: 4px !important;

                .nep-select-ellipsis {
                    padding-bottom: 0px !important;
                }
            }
        }
    }
}

.group-input {
    input {
        padding-left: 0px !important;
    }

    input::placeholder {
        font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;
    }
}

.group-text::placeholder {
    font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;
}

.group-menu {
    background: #FFFFFF 0% 0% no-repeat padding-box !important;
    box-shadow: 0px 0px 6px #00000014 !important;
    border-radius: 4px !important;

    .mat-mdc-menu-content {
        button {
            padding-left: 12px;
            padding-right: 12px;
            color: #212121;
            line-height: 40px;
            height: 40px;
        }

        .mat-menu-item {
            border-bottom: 1px solid #eee !important;
        }

        .mat-menu-item:last-child {
            border-bottom: none !important;
        }

        .mat-menu-item:hover {
            background: #EFF0F9 0% 0% no-repeat padding-box !important;
        }
    }
}

.custom-user-access-table>.p-datatable .p-datatable-thead>tr>th {
    border-top: none !important;
}

.custom-user-access-table>.p-datatable .p-datatable-tbody>tr>td:first-child,
.custom-user-access-table>.p-datatable .p-datatable-thead>tr>th:first-child {
    border-left: none !important;
}

.lp-report-Kpilineitems {
    .p-multiselect {
        width: 240px !important;
        height: 32px !important;
        background-color: #FFFFFF !important;
        border: 1px solid #CAC9C7 !important;
        border-radius: 4px !important;

        .p-multiselect-label-container {
            padding-top: 1px !important;

            .p-multiselect-label {
                color: #000000;
            }

            .p-multiselect-trigger {
                .p-multiselect-trigger-icon {
                    padding-top: 4px !important;
                }
            }
        }
    }

    .p-dropdown-trigger,
    .p-autocomplete-dropdown,
    .p-multiselect-trigger {
        padding-top: 8px !important;
    }
}

.loading-file {
    width: 24px !important;
}

.mat-mdc-feature-checkbox {
    width: 20px !important;
    height: 20px !important;
}

.feature-tree>.p-treetable {
    margin-top: 0px !important;
}

.p-treetable .p-treetable-thead>tr>th {
    background-color: #FAFAFB !important;
    border-color: #CAC9C7 !important;
    color: #000000 !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    font-family: "Helvetica Neue LT W05_65 Medium", Arial, Verdana, Tahoma, sans-serif;
    border: 1px solid #dee2e6 !important;
    padding: 12px 16px !important;
}

.p-treetable .p-treetable-thead>tr>th:first-child,
.p-treetable .p-treetable-thead>tr>th:last-child {
    border-left: none !important;
}

.custom-kpi-table>.p-treetable .p-treetable-thead>tr>th,
.p-treetable-scrollable-body>table>.p-treetable-tbody>tr>td {
    border-left: none !important;
}

.custom-kpi-table>.p-treetable .p-treetable-tbody>tr>td:first-child {
    border-left: none !important;
}

.p-treetable-scrollable-body>table>.p-treetable-tbody>tr:first-child>td {
    border-top: none !important;
}

.features-toggle-checkbox>.p-checkbox .p-checkbox-box>.pi-minus {
    background-color: $nep-primary;
    padding: 2px;
    color: $nep-white !important;
    border: 1px solid $nep-primary;
    border-radius: 4px;
}

#status-add-dialog>div>div.nep-card.nep-card-shadow.nep-modal-panel.nep-modal-default>div.nep-card-header.nep-modal-title>p-toast>div {
    width: 63% !important;
    left: 50% !important;
    top: 0 !important;
}

#status-add-dialog>.nep-modal>.nep-card>.nep-card-body {
    padding: 16px 4px !important;
}

#status-add-dialog>div>div.nep-card.nep-card-shadow.nep-modal-panel.nep-modal-default>div.nep-card-body>div>div.customselect-div.ng-star-inserted>div:nth-child(2)>typeahead-control>p-autocomplete>span>button>span.p-button-icon-left.p-clickable.pi.pi-caret-down {
    color: #000000 !important
}

#status-add-dialog.AddOrUpdateKpi>.nep-modal>.nep-card>.nep-card-body {
    padding: 0px !important;
}

.tree-toggler {
    button {
        padding-top: 10px !important;
    }
}

.access-subfeature {
    .p-dropdown {
        width: 240px;
        height: 32px;
        background-color: #FFFFFF !important;
        border: 1px solid #CAC9C7;
        border-radius: 4px !important;
        padding-left: 8px;

        .p-dropdown-label {
            padding-top: 4px;
            color: #000000;
            opacity: 1 !important;
            font-size: 14px;
        }

        .p-dropdown-panel {
            margin-top: 4px;
            box-shadow: 0 0 1px 1px rgb(0 0 0 / 2%), 0 6px 12px rgb(0 0 0 / 18%);
            transform-origin: 0 0;

            .p-dropdown-items {
                padding: 0px !important;

                .p-dropdown-item {
                    padding: 12px 16px;
                    padding-right: 8px !important;
                    text-align: left;
                    letter-spacing: 0px;
                    color: #55565A !important;
                    opacity: 1;

                    &:hover {
                        background-color: #F0F0F1 !important;
                        cursor: pointer;
                    }
                }

                .p-highlight {
                    background-color: #F0F0F1 !important;
                }
            }
        }
    }

    .p-multiselect {
        width: 240px !important;
        height: 32px;
        background-color: #FFFFFF !important;
        border: 1px solid #CAC9C7;
        border-radius: 4px !important;
        padding-left: 8px;

        .p-multiselect-trigger {
            padding: 8px 8px 8px 0px;
        }
    }
}

.user-country {
    p-autocomplete {
        .p-autocomplete {
            input {
                padding-left: 0px !important;
            }
        }
    }
}

.is-country-invalid {
    .p-autocomplete {

        input,
        button {
            border-bottom: 1px solid #c62828 !important;
        }
    }
}

.custom-p-dropdown {
    .p-dropdown {
        background-color: #FFFFFF !important;
        border: 1px solid #CAC9C7 !important;
        border-radius: 4px;
        cursor: pointer;
        padding-left: 0px !important;
        height: 32px !important;
        color: #000000;
        opacity: 1 !important;
        font-size: 14px;
        width: 240px !important;

        .p-dropdown-label {
            color: #000000;
            padding-left: 8px;
        }

        .p-dropdown-item {
            padding: 8px;
        }
    }
}

.custom-p-dropdown.valuation {
    .p-dropdown {
        width: 180px !important;

        .p-dropdown-label {
            padding-top: 5px;
        }

        .p-dropdown-trigger {
            padding-top: 6px;
        }
    }
}

.subfeature-tbl {
    .p-datatable {
        thead {
            .checkbox-header {
                padding-top: 11px !important;
                padding-bottom: 11px !important;
            }
        }
    }
}

.edit-section-header {
    margin-top: 5px;
    background: $nep-base-grey 0% 0% no-repeat padding-box;
    opacity: 1;
}

.workflow-section-footer {
    box-shadow: 0px 3px 6px #00000014;
}

.company-inform {
    border: 1px solid $nep-light-border;
}

.historycomments-p {
    padding: 8px 20px;

    .btn:focus,
    .btn.focus {
        box-shadow: none;
        border: 1px solid $nep-primary !important;
    }

    .btn-nep-default {
        background: #FFFFFF 0% 0% no-repeat padding-box;
    }

    button {
        border: 1px solid $nep-primary;
        border-radius: 4px;
        font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;
    }
}

.lp-nep-input {
    input {
        padding: 0px !important;
    }
}

.history-pl-pr {
    padding-left: 8px !important;
    padding-right: 8px !important;
}

.custom-kpi-calendar {
    input {
        padding-left: 16px !important;
        border: 1px solid #eee !important;
        border-right: none !important;
    }

    .p-button.p-button-icon-only {
        border: 1px solid #eee !important;
        border-radius: 0px 4px 4px 0px !important;
    }

    input::-webkit-input-placeholder,
    input::-ms-input-placeholder,
    input::placeholder {
        font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;
        color: $nep-text-color;
        opacity: 1;
        font-size: 12px !important;
    }
}

.draft-select {
    .p-dropdown {
        width: 200px !important;

        .p-placeholder {
            color: #ABABB8 !important;
        }
    }
}

.custom-pipeline-tab {
    .nep-tabs-tab {
        padding-top: 12px !important;
        padding-bottom: 12px !important;
        @extend .Body-R;
        color: #666666;
    }
    .nep-tabs-active{
        color: $nep-primary !important;
        @extend .Body-R;
    }
}

.pipeline-table,
.fundlist-table {
    table tbody>tr>td {
        // border: none !important;
        padding: 16px 20px;
        letter-spacing: 0px;
        color: #212121 !important;
        opacity: 1 !important;
    }

    table tbody>tr {
        border-bottom: 1px solid #DEDFE0 !important;

        &:hover {
            background: #EFF0F9 0% 0% no-repeat padding-box;
            cursor: pointer;
        }
    }

    table tbody>tr:last-child {
        border-bottom: none !important;
    }

    .p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody>tr>td:last-child {
        border-width: 0 1px 1px 1px !important;
    }

    .p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody>tr>td:first-child {
        border-left: none !important;
    }

    .p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody>tr:last-child>td {
        border-bottom: none !important;
    }
}

.fundlist-table {
    table tbody>tr>td {
        width: 100% !important;
        padding-left: 14px !important;
    }

    table tbody>tr>td>a {
        letter-spacing: 0px;
        color: #212121 !important;
        opacity: 1 !important;
    }

    .p-table {
        table tbody>tr>td:first-child {
            padding-left: 32px !important;
        }
    }
}

.fundlist-tree-table {
    table tbody>tr>td {
        padding-top: 18px !important;
        padding-bottom: 18px !important;
    }
}

.add-pipeline {
    input {
        padding-left: 0px !important;
    }
}

.newValueBackground {
    background: #FFECB3 !important;
}

.pipeline-chart {

    .highcharts-legend-item,
    .highcharts-axis-title {
        display: none !important;
    }

    .highcharts-root {
        .highcharts-data-labels {
            .highcharts-text-outline {
                color: transparent !important;
            }
        }
    }
}

.addCommentPopup {
    height: 300px;
    border: 1px solid #e0e0e0;
    border-radius: 4px !important;
}

.addCommentTextArea {
    resize: none !important;
    height: inherit !important;
    width: -webkit-fill-available !important;
    max-height: 300px !important;
    font-size: 14px !important;
}

.p-dropdown-panel.p-component.ng-star-inserted {
    max-width: 320px !important;
}

.custom-scroller-position .p-dropdown-panel.p-component.ng-star-inserted {
    max-width: 240px !important;
    position: fixed;
    width: 100% !important;
}

tspan.highcharts-text-outline {
    fill: transparent !important;
    stroke: transparent !important;
    color: transparent !important;
}

.icon-dropdown {
    .p-dropdown {
        padding-top: 0px !important;
    }
}

.fundlist-expand {
    .p-button-icon {
        padding-top: 5px !important;
    }
}

.custom-choose-section {
    .p-dropdown {
        border: 1px solid #4061C7 !important;
    }
}

.cashflow-fund {
    input {
        padding-left: 0px !important;
    }
}

.fund-expanded,
.fund-collapsed {
    width: 24px;
    height: 24px;
    margin-top: 4px !important;
}

.fund-expanded {
    background-image: url("assets/dist/images/Expand-more.svg");
}

.fund-collapsed {
    background-image: url("assets/dist/images/Expand-less.svg");
}

.FCinfoIconImg {
    height: 16px;
    width: 16px;
    margin-left: 8px;
    margin-top: -2px;
}

.popover {
    max-width: 500px !important;
    // left:-49%!important;
    margin-top: 0px !important;
    margin-left: 0px !important;
    margin-bottom: 0px !important;
}

.popover .arrow {
    display: none !important;
}

.popover-body {
    padding: 0px !important;
}

div>p-calendar>span>div {
    top: 0px !important;
}

.fund-custom-dropdown {
    .p-dropdown-label {
        overflow: inherit !important;
        padding-right: 12px !important;
    }

    .p-inputtext {
        width: 100% !important;

        .custom-label {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
            padding-top: 1px;
        }

        img {
            margin-top: -12px;
            padding-right: 4px;
        }
    }

    .custom-ui-label {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 80%;
    }
}

.workflow-toggle {
    .mat-button-toggle-appearance-standard .mat-button-toggle-label-content {
        line-height: 32px !important;
        padding: 0 16px !important;
    }

    .mat-button-toggle-group-appearance-standard {
        background: #FFFFFF 0% 0% no-repeat padding-box;
        border: 1px solid #4061C7;
    }

    .mat-button-toggle-appearance-standard {
        color: #4061C7;
    }

    .mat-button-toggle-checked {
        background: #4061C7 0% 0% no-repeat padding-box;
        border: 1px solid #4061C7;
        text-align: center;
        letter-spacing: 0px;
        color: #FFFFFF;
        opacity: 1;
    }

    .mat-button-toggle-group-appearance-standard .mat-button-toggle+.mat-button-toggle {
        border-left: 1px solid #4061C7;
    }

    .mat-button-toggle-checked {
        height: 32px;
        border-top-width: 0px;
        border-bottom-width: 0px;
    }
}

.p-datatable-frozen-view .p-datatable-scrollable-body {
    border-right: 1px solid $nep-divider !important;
    overflow: hidden !important;
}

body>app>master>div>div>div>div>ng-component>cashflow>div:nth-child(2)>div.col-12.col-sm-12.col-md-12.col-xl-12.col-lg-12.outer-section.pl-0.pr-0>div.content-bg>div>div>p-table>div>div.p-datatable-scrollable-wrapper.ng-star-inserted>div.p-datatable-scrollable-view.p-datatable-frozen-view.ng-star-inserted>div.p-datatable-scrollable-header {
    border-right: 1px solid $nep-divider !important;
}

#neptab>div>div.nep-tabs-panel.nep-tabs-show>div.ng-star-inserted>app-profit-and-loss>div>div>div>p-table>div>div>div.p-datatable-scrollable-view.p-datatable-frozen-view.ng-star-inserted::-webkit-scrollbar {
    width: 0px !important;
}

// .p-datatable-scrollable-body,
// .p-treetable-scrollable-body {
//     overflow: auto !important;
// }
.workflow-feature-section {
    .mat-custom-checkbox {
        label {
            margin-top: 0px !important;
        }
    }
}

.fund-calendar {
    width: 100%;
}

.fundlist-table {
    .p-datatable-scrollable-body {
        overflow-y: auto !important;
    }
}

.highcharts-axis-labels span {
    white-space: nowrap !important;
}

.custom-td-first {
    line-height: 44px !important;
}

.zero-state-rp {
    .zeroStateKpi {
        border: none !important;
        border-top: 1px solid #CAC9C7 !important;
        background: none !important;
        height: 30vh !important;
        border-radius: 0px !important;

        div:first-child {
            margin-top: 5% !important;
        }
    }
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.page-custom-dropdown .p-dropdown .p-dropdown-label {
    padding-top: 5px !important;
    padding-left: 12px !important;
}

.p-tooltip.bg-grey-color .p-tooltip-text {
    background-color: $gray-900 !important;
    color: #55565A !important;
    font-size: 0.875rem;
}

.p-tooltip.bg-grey-color.p-tooltip-right .p-tooltip-arrow {
    border-right-color: $gray-900 !important;
}

.p-tooltip.bg-grey-color.p-tooltip-left .p-tooltip-arrow {
    border-left-color: $gray-900 !important;
}

.p-tooltip.bg-grey-color.p-tooltip-top .p-tooltip-arrow {
    border-top-color: $gray-900 !important;
}

.p-tooltip.bg-grey-color.p-tooltip-bottom .p-tooltip-arrow {
    border-bottom-color: $gray-900 !important;
}

.p-tooltip.draft-p-tooltip-text .p-tooltip-text {
    border: 1px solid #DEDFE0 !important;
}

.field-nep-button {
    .nep-button {
        padding-left: 10px;
    }
}

.field-hide-toggle {
    .p-inputswitch {
        top: 5px !important;
    }
}

.box-columns {
    background: $nep-white 0% 0% no-repeat padding-box;
    border: 1px solid $nep-divider;
    border-radius: 4px;
    opacity: 1;
    padding: 4px 12px;
    block-size: fit-content;
    max-width: 100% !important;
    display: inline-flex;

    &:hover {
        border: 1px solid $nep-divider;

        .company-buttons {
            display: block;
        }
    }

    span {
        width: 100%;
        width: fit-content;
        block-size: fit-content;
    }
}

.download-excel {
    cursor: pointer;
}


.mat-side-bar {
    .mat-drawer-inner-container {
        overflow-y: hidden !important;
    }

    .custom-nav-child {
        .mat-mdc-list-item {
            padding-left: 0px !important;
            height: 40px !important;
        }

        .mdc-list-item__content {
            padding-left: 30px !important;
        }

        .child-mdc-list-item__content:hover {
            background: rgb(***********/ 10%) !important;
        }
    }


    .mat-nav-custom {
        height: calc(100% - 167px) !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;

        .mat-nav-custom-item {
            height: 2.5em !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
        }
    }

    .mat-drawer {
        overflow-x: hidden !important;
    }

    .mat-expansion-panel-header .mat-icon {
        padding: 0px !important;
        box-sizing: content-box !important;
    }

    .mat-icon {
        padding: 0px !important;
        box-sizing: content-box !important;
    }

    .mdc-list-item__content {
        padding: 0px 8px 0px 12px !important;

        .mat-child-nav-item {
            padding-left: 32px !important;
        }

        .sidenav-item {
            padding-left: 12px !important;
            align-self: center;
        }

        .sidenav-item:hover {
            background: none !important;
        }

        .side-bar-font {
            width: 12em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .mat-child-nav-font {
            padding-top: 0px !important;
            width: 11em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .side-bar-font:hover {
            background: none !important;
        }

        .mat-icon:hover {
            background: none !important;
        }

        .mat-icon-img:hover {
            background: none !important;
        }

        .mat-child-nav-item:hover {
            background: none !important;
        }

        .mat-icon-custom {
            // padding: 0px 5px 4px 4px !important;
            align-self: center;
            height: auto !important;
        }
    }

    .custom-expansion-panel {
        background: transparent !important;
        border-radius: 0px !important;

        .mat-expansion-panel-header {
            padding: 0px 8px 0px 12px !important;
            height: 40px !important;
        }

        .mat-expansion-indicator {
            padding-bottom: 5px !important;
        }

        .mat-expansion-panel-header-description,
        .mat-expansion-indicator::after {
            color: #fff !important;
        }

        .mat-icon {
            height: auto !important;
        }
    }

    .side-nav-logo {
        overflow: hidden;
        display: inline-flex;
        align-items: center;
        padding: 5px 0px 5px 0px;
        height: 4.75em;
    }
}

.mat-sidenav::-webkit-scrollbar {
    width: 10px !important;
    height: 10px !important;
    background-color: #faa !important;
}

.mat-sidenav::-webkit-scrollbar-thumb {
    background: #f00 !important;
}

.mat-drawer-content {
    overflow: inherit !important;
    z-index: 1050 !important;
}

.mat-sidenav {
    .mdc-list-item__primary-text {
        display: flex;
        flex-direction: row;
        align-items: center;
        box-sizing: border-box;
        position: relative;
        height: 2.5rem !important;
    }

    .mat-expansion-panel {
        .mat-content {
            margin-right: 0px !important;
        }
    }
}

.sidenav-arrow {
    padding: 8px 24px 20px 20px !important;

    .fa-custom {
        font-size: 1.8em;
        color: #FFFFFF;
        cursor: pointer;
    }
}

.triangle {
    position: absolute;
    right: -34px;
    width: 38px;
    height: 72px;
    transform: rotate(45deg);
    background: white;
}

.mat-nav-custom-item {
    .custom-mat-list-content:hover {
        background: none !important;
    }
}

.mat-nav-custom-item :hover {
    background: rgb(***********/ 10%) !important;
}

.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:hover:not([aria-disabled=true]) {
    background: rgb(***********/ 10%) !important
}

.reset-update-portfolio-css {
    width: 120px;
    height: 32px;
    border-radius: 4px !important;
}


.mat-drawer::-webkit-scrollbar {
    display: unset !important;
    background-color: rgba(128, 128, 128, 0.5) !important;
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgb(0 0 0 / 30%) !important;
    height: 10% !important;
    max-height: 20% !important;
}

.mat-drawer::-webkit-scrollbar-track {
    display: unset !important;
    background-color: rgba(128, 128, 128, 0.5) !important;
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgb(0 0 0 / 30%) !important;
    height: 10% !important;
    max-height: 20% !important;
}

.mat-drawer::-webkit-scrollbar-thumb {
    display: unset !important;
    background-color: rgba(128, 128, 128, 0.5) !important;
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgb(0 0 0 / 30%) !important;
    height: 10% !important;
    max-height: 20% !important;
}

.mat-drawer::-webkit-scrollbar-thumb:hover {
    display: unset !important;
    background-color: rgba(128, 128, 128, 0.5) !important;
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgb(0 0 0 / 30%) !important;
    height: 10% !important;
    max-height: 20% !important;
}

.mat-side-bar {
    .mat-expansion-panel-body {
        padding: 0 !important;
    }

    .mat-divider {
        margin-top: 16px;
        margin-bottom: 16px;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .is-active {
        background-color: rgb(***********/ 20%);
    }
}

.mat-sidenav-content {
    background-color: #fff !important;
}

.dealDataContainer {
    margin-left: 0px !important;
    margin-right: 0px !important;
    max-height: calc(100vh - 175px);
}

.datatype-cntrl {
    p-autocomplete {
        .p-autocomplete {
            .p-autocomplete-panel {
                max-height: 245px !important;
            }

        }
    }
}

.xlModal>.modal-dialog {
    max-width: 971px !important;
    min-width: 971px !important;
    width: 95% !important;
}

.allvalues-kpis {
    float: left;
    font-size: 12px;
    margin-right: 12px;
    color: #75787B;
    padding-left: 16px !important;
    padding-top: 8px !important;
}

.fixed-footer {
    position: fixed;
    bottom: 0;
    right: 0;
    background: #FAFAFB 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 6px #00000014;
    border: 1px solid #DEDFE0;
    opacity: 1;
    padding-top: 4px;
    padding-bottom: 2px;
}

.dealLoader {
    font-size: 2rem;
    padding-left: 50%;
    padding-top: 19%;
}

.dealsContainer {
    background: $nep-white 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000014;
    border: 1px solid $nep-divider;
    border-radius: 4px;
    opacity: 1;
    padding-top: 1.25rem !important;
}

.padding-left-rem {
    padding-left: 0.75rem;
}

.padding-rem {
    padding-bottom: 1.25rem;
    padding-left: 1.25rem !important;
    padding-right: 1.25rem !important;
}

.padding-card-rem {
    padding-bottom: 1.25rem;
    padding-left: 0 !important;
    padding-right: 1rem !important;
}

.mat-drawer-container {
    background-color: transparent !important;
}

.bold-text {
    font-weight: bold !important;
}

.headerKpi {
    font-family: 'Helvetica Neue LT W05_65 Medium', Arial, Verdana, Tahoma, sans-serif !important;
}

.financials-bold-text {
    font-weight: bold !important;
    // margin-left: 9px!important;
}

.financialsHeaderKpi {
    font-family: 'Helvetica Neue LT W05_65 Medium', Arial, Verdana, Tahoma, sans-serif !important;
}

.circle-loader {
    .fa-circle-o-notch {
        color: #FFFFFF !important;
    }
}

.multi-select-custom {
    .p-multiselect-items-wrapper {
        overflow: hidden !important;
    }
}

.custom-overlay-panel {
    .p-overlaypanel {
        z-index: 1000 !important;
    }
}

.fund-list-table {
    thead {
        tr {
            border-bottom: 1px solid $nep-divider !important;
        }
    }

    tr {
        padding: 12px 16px !important;

        td {
            padding: 12px 16px !important;
        }
    }

    .p-datatable-scrollable-header-box {
        // padding-right: 0px !important;
        background: #FAFAFB 0% 0% no-repeat padding-box !important;
    }
}

.fund-tr-model,
.fund-static {

    input,
    select,
    .p-button {
        height: 32px;
    }

    input::placeholder,
    select::placeholder {
        letter-spacing: 0px;
        color: #ABABB8 !important;
    }
}

.fund-splitButton {
    .p-button {
        background: $nep-primary 0% 0% no-repeat padding-box !important;
        height: 32px !important;
    }

    .p-splitbutton-defaultbutton {
        border-radius: 4px 0px 0px 4px !important;
        padding-left: 16px;
        padding-right: 8px;
    }

    .p-splitbutton-menubutton {
        border-radius: 0px 4px 4px 0px !important;
        border-bottom: none !important;
        padding-left: 0px;
        padding-right: 16px;
    }

    .p-button:enabled:hover {
        background: $nep-primary !important;
        border-color: $nep-primary !important;
        color: $nep-white !important;
    }

    .p-button.p-button-icon-only {
        color: $nep-white !important;
    }

    .p-menu {
        width: 11.5rem !important;
    }
}

.custom-geo {
    .row {
        margin-left: 0px !important;
        margin-right: 0px !important;
        padding-left: 16px !important;
        padding-right: 16px !important;
        padding-top: 0px !important;
    }
}

.fund-static {
    .p-button-icon-only {
        height: 32px !important;
    }
}

.mb-section {
    margin-bottom: 50px;
}

.custom-btn-split {
    .fa-file-pdf-o {
        color: red !important
    }

    .fa-file-excel-o {
        color: green !important;
    }
}

.custom-investor-menu {
    width: 260px !important;

    .investor-menu-header {
        background: #FAFAFB 0% 0% no-repeat padding-box;
        border-top: 1px solid #DEDFE0;
        border-bottom: 1px solid #DEDFE0;
        border-radius: 4px 4px 0px 0px;
        height: 40px;
        padding: 10px 12px 12px 16px;

        .invest-header {
            letter-spacing: 0.17px;
            color: #000000;
            font-family: "Helvetica Neue LT W05_65 Medium", Arial, Verdana, Tahoma, sans-serif;
        }

        .close-img {
            margin-top: -4px;
            cursor: pointer;
        }
    }

    .custom-invest-btn {
        background: #FFFFFF 0% 0% no-repeat padding-box;
        border-bottom: 1px solid #DEDFE0;
        border-radius: 0px 0px 4px 4px;
        height: 44px;
        padding-left: 16px !important;
        padding-right: 16px !important;
        padding-top: 12px !important;
        padding-bottom: 12px !important;
        line-height: 0px !important;

        .invest-link {
            letter-spacing: 0.17px;
            color: #212121;
        }
    }

    .custom-invest-btn:hover {
        background: #F7F8FC 0% 0% no-repeat padding-box;

        .invest-link {
            text-decoration: underline;
            color: #4061C7;
            cursor: pointer;
        }
    }
}

.invested-table {
    .p-datatable-scrollable-body {
        border-right: none !important;
    }
}

.list-table {
    tr {

        &:hover {
            background: $nep-row-active 0% 0% no-repeat padding-box !important;
            border: 1px solid $nep-divider;
        }
    }
}

.pc-table {

    th,
    th:hover,
    thead:hover,
    thead>tr:hover {
        border-top: none !important;
        border-left: none !important;
    }
}

.click-view:hover {
    text-decoration: underline !important;
}

.mandatory-label:after {
    content: "*";
    color: red;
}

.frozenColNoDataCss {
    border-right: none !important;
}

.investor-tr-table>.p-datatable .p-datatable-tbody>tr:last-child>td {
    border-bottom: 1px solid #d5d5d5 !important;
}

.investor-tr-table>.p-datatable .p-datatable-tbody>tr>td {
    border-right: 1px solid #d5d5d5 !important;
}

.investor-tr-table>.p-datatable .p-datatable-tbody {
    position: sticky !important;
}

.empty-state-text {
    letter-spacing: 0px;
    color:#7E7E8C;
    opacity: 1;
}

.kpi-list-table {
    .p-datatable .p-datatable-header {
        border: none !important;
        padding: 0px !important;
        background: #FAFAFB !important;
        margin-top: -1px;
    }

    .custom-select {
        width: 200px !important;
    }

    td:first-child {
        border-left: none !important;
    }
}

.image-container {
    text-align: center !important;
    padding-top: 9% !important;
}

.tree-chk-box {
    .mat-checkbox-inner-container {
        margin-right: 16px !important;
    }
}

.mat-tree-node {
    width: 100% !important;

    .drag-icon {
        cursor: pointer !important;
    }

    .mat-checkbox {
        max-width: 90% !important;
    }

    .mat-checkbox-inner-container {
        margin-right: 16px !important;
    }

    background-color: transparent;
    letter-spacing: 0.17px;
    color: #212121;
    user-select: none;
    cursor: move;
    border-right: 1px solid #DEDFE0;

    &:hover {
        background: #F5F6FB 0% 0% no-repeat padding-box;
        color: #212121;
    }

}

.mapping-copyto-select {
    .p-multiselect {
        background-color: transparent !important;
        border-top: none !important;
        border-left: none !important;
        border-right: none !important;
        border-bottom: 1px solid #CAC9C7 !important;
        height: 28px !important;
        width: 240px !important;
        border-radius: 0px !important;

        .p-multiselect-trigger {
            background-color: transparent !important;
        }

        .p-placeholder {
            color: #ABABB8 !important;
        }
    }
}

.mat-custom-nodes {
    .mat-checkbox-label {
        letter-spacing: 0.17px;
        white-space: initial;
    }

    .duplicate-kpi {
        position: absolute;
        right: 0px;
    }

    .mat-tree-node:hover {
        .duplicate-kpi {
            display: block;
        }

        .kpi-header {
            display: inline-block;
        }

        .formula-image {
            display: inline-block !important;
        }
    }
}

.header-zero-state {
    letter-spacing: 0px;
    color: #55565A;
    font-family: "Helvetica Neue LT W05_65 Medium", Arial, Verdana, Tahoma, sans-serif;
}

.message-zero-state {
    letter-spacing: 0px;
    color: #ABABAB;
    font-size: 12px;
}

.p-tooltip.bg-grey-tooltip-color .p-tooltip-text {
    background: #F5F5F5 0% 0% no-repeat padding-box !important;
    color: #55565A !important;
    font-size: 0.875rem;
    border: 1px solid #DEDFE0;
}

.p-tooltip.bg-grey-tooltip-color.p-tooltip-bottom .p-tooltip-arrow {
    border-bottom: 1px solid #DEDFE0 !important;
}

.kpi-header {
    cursor: pointer;
}

.fixed-calculation-table {
    .p-datatable-tbody {
        tr:last-child {
            position: sticky !important;
            bottom: -3px !important;
        }
    }
}

.topholding-filter-dopdown>.p-dropdown {
    border-radius: 0 !important;
    border-bottom: 1px solid #eee !important;
    width: 260px;
    height: 32px;
    font-size: 0.75rem;
    padding-left: 12px !important;
}

.default-text-color>.nep-input {
    color: #212121 !important;
}

.default-filter-txtcolor>.nep-input {
    color: #75787B !important;
}

.holding-mat-menu {
    min-width: none !important;
    max-width: none !important;
}

.top-holding-multiselect {
    .p-multiselect .p-multiselect-label {
        padding: 8px 0px 8px 0px !important;
    }

    .p-multiselect {
        padding-left: 12px !important;

        .p-multiselect-trigger {
            padding-top: 8px;
        }

        .p-multiselect-panel {
            width: 100% !important;
        }
    }
}

.top-holding-panel {
    .p-link:focus {
        box-shadow: none !important;
    }

    .p-panel-header,
    .p-panel-footer {
        background: #FAFAFA !important;
        border: 1px solid #DEDFE0 !important;
        box-shadow: 0px 3px 6px #00000014;
        padding-right: 16px !important;
    }

    .p-panel-content {
        border-radius: 0 !important;
        padding: 0 !important;
        padding-bottom: 1.25rem !important;
        border-bottom: none !important;
    }

    .p-panel-header-icon {
        padding-top: 8px !important;
        padding-bottom: 8px !important;

        .pi-chevron-down,
        .pi-chevron-up {
            padding-top: 5px !important;
        }
    }
}

.quarterYearCntrl {
    .cursorBut {
        border: none !important;
        border-bottom: 1px solid #CAC9C7 !important;
        border-radius: 0px !important;
        height: 36px !important;
    }

    .p-tooltip.bg-grey-tooltip-color.p-tooltip-bottom .p-tooltip-arrow {
        border-bottom: 1px solid #DEDFE0 !important;
    }
}

.top-holding-table {

    .p-component,
    .p-component {
        box-sizing: none !important;
    }
}

.top-holding-lastrow .p-datatable-scrollable-body {
    overflow-x: scroll !important;

    div {
        height: 0px !important;
    }

}

.top-holding-lastrow {
    .p-datatable .p-datatable-tbody>tr:last-child {
        background: #E8EAF6 !important;
        opacity: 1;
        box-shadow: 0px -3px 6px #00000014, 0px 3px 6px #00000014;
        -webkit-box-shadow: 0px -3px 6px #00000014, 0px 3px 6px #00000014;
        -moz-box-shadow: 0px -3px 6px #00000014, 0px 3px 6px #00000014;

        .higlighted-cell {
            background: #E8EAF6 !important;
            opacity: 1;
        }
    }
}

.top-holding-section {

    .p-dropdown-label,
    .p-multiselect-label {
        letter-spacing: 0px;
        color: #75787B !important;
        opacity: 1;
    }

    .p-inputwrapper-filled {
        .p-dropdown-label {
            color: #000000 !important;
            letter-spacing: 0px !important;
        }

        .p-multiselect-label {
            color: #212121 !important;
        }
    }
}

.custom-quarter-year {

    button,
    .drop-show {
        width: 100% !important;
    }
}

.nep-border-b {
    border-bottom: 1px solid #DEDFE0;
}

.filter-txt-color {
    input {
        letter-spacing: 0px !important;
        color: #212121 !important;
        opacity: 1 !important;
    }
}

.pc-border-bottom {
    border-bottom: 1px solid #dee2e6 !important;
}

.custom-formula-select {
    .nep-input {
        border-left: none !important;
        border-right: none !important;
        border-top: none !important;
        border-radius: 0px !important;
        background: transparent !important;
    }

    .nep-select-ellipsis {
        letter-spacing: 0px !important;
        color: #212121 !important;
    }

    .pi-chevron-down {
        color: #50505C !important;
    }
}

.company-info-virtual-port {
    .cdk-virtual-scroll-content-wrapper {
        width: 100% !important;
    }
}

.p-multiselect-item {
    height: auto !important;
}

.p-multiselect-items-wrapper {
    .cdk-virtual-scroll-content-wrapper {
        width: 100% !important;
    }

    .p-multiselect-item {
        span {
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;
        }
    }
}

textarea::placeholder {
    font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;
}

.formula-kpi-table {
    height: 300px !important;
    background: $nep-white !important;
}

@media (min-width: 980px) and (max-width: 1024px) {
    .formula-builder-model {
        .formula-btns {
            padding-top: 0px !important;
        }

        .custom-select {
            width: 160px !important;
        }

        .formula-btn:nth-last-child(2),
        .formula-btn:nth-last-child(1) {
            margin-top: 8px;
            background-color: $nep-white;
        }
    }
}

@media (min-width: 1025px) and (max-width: 1280) {
    .formula-builder-model {
        .nep-card {
            width: 64rem !important;
        }

        .custom-select {
            max-width: 200px !important;
            width: 180px !important;
        }

        .formula-btn:nth-last-child(2) {
            margin-top: 8px;
            background-color: $nep-white;
        }

        .formula-btn:nth-last-child(1) {
            margin-top: 8px;
            background-color: $nep-white;
        }
    }
}

@media (min-width: 1280px) {
    .formula-builder-model {
        .nep-card {
            width: 64rem !important;
        }

        .custom-select {
            max-width: 200px !important;
        }
    }
}

.p-dropdown {
    .cdk-virtual-scroll-content-wrapper {
        width: 100%;

        .p-dropdown-item {
            width: 100%;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;
        }
    }
}

.custom-auto-complete {
    input {
        width: 240px !important;
        border-bottom: none !important;
    }

    .p-autocomplete {
        background-color: #FFFFFF !important;
        border: 1px solid #CAC9C7 !important;
        border-radius: 4px !important;
        padding-left: 8px;
    }

    .p-autocomplete-dropdown {
        border: none !important;
        border-radius: 0px !important;
        background-color: transparent !important;

        &:hover {
            background-color: transparent !important;
        }
    }
}

.custom-searchable-dropdown {
    .p-dropdown-filter {
        padding: 6px 8px 6px 8px !important;
        border: 1px solid #c9c9d4 !important;
    }

    .p-dropdown-header {
        padding: 8px !important;
    }

    .p-dropdown-filter-icon {
        padding-top: 2px !important;
        position: absolute;
        top: 50%;
        margin-top: -0.5rem;
    }
}

.custom-searchable-multiselect {
    .p-multiselect-filter {
        padding: 6px 8px 6px 8px !important;
        border: 1px solid #c9c9d4 !important;
    }

    .p-multiselect-filter-icon {
        padding-top: 2px !important;
    }

    .p-multiselect-header {
        padding: 8px 0px 8px 8px !important;
    }

    .p-multiselect-close {
        margin-left: 0px !important;
    }
}

.custom-mat-tab {
    .mat-tab-header-pagination-before {
        border-right: 1px solid $nep-nav-tab-border-color;
        background: $nep-white;
        border-bottom: 1px solid $nep-nav-tab-border-color;
        margin-right: 20px;
    }

    .mat-tab-header-pagination-after {
        border-left: 1px solid $nep-nav-tab-border-color;
        background: $nep-white;
        border-bottom: 1px solid $nep-nav-tab-border-color;
        margin-left: 20px;
    }

    .mat-mdc-tab-link {
        height: 34px !important;
        padding-left: 16px !important;
        padding-right: 16px !important;
        background: $nep-white !important;
        flex-grow: 0 !important;
        .mdc-tab__text-label {
            color: #666666 !important;
            @extend .Body-R;
        }

        .mdc-tab-indicator {
            &::hover {
                background: transparent !important;
            }
        }
    }

    .mat-mdc-tab-link:hover {
        color: #4061C7 !important;
        background: $nep-white !important;
    }

    .mat-mdc-focus-indicator:hover,
    .mat-mdc-focus-indicator:focus,
    .mat-mdc-focus-indicator:focus-within,
    .mat-mdc-focus-indicator {
        background: transparent !important;
    }

    .mdc-tab--active:focus,
    .mdc-tab--active:focus-within {
        background: $nep-white !important;
    }

    .mat-mdc-tab-header-pagination {
        padding-left: 0px !important;
        width: 44px !important;
        min-width: 44px !important;
    }

    .mdc-tab--active {
        position: relative !important;
        background-color: $nep-white !important;
        border: 1px solid $nep-nav-tab-border-color ;
        border-top-left-radius: 0.25rem;
        border-top-right-radius: 0.25rem;
        border-bottom: none !important;

        .mdc-tab__text-label {
            color: #4061C7 !important;
            @extend .Body-R;
        }

        &::hover {
            background-color: $nep-white !important;
        }
    }

    .mdc-tab--active:hover {
        background-color: $nep-white !important;
    }

    .mat-mdc-tab-link-container {
        background-color: $nep-white !important;
        padding-top: 8px !important;
        letter-spacing: 0px !important;
        opacity: 1 !important;

        &::before {
            border-top: 1px solid #dee2e6;
            content: "";
            margin: 0 auto;
            position: absolute;
            top: 98%;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            z-index: -1;
        }
    }

    .mat-ink-bar {
        display: none !important;
    }

    .mat-mdc-tab-header {
        // margin-bottom: -11px !important;
        border-bottom: none !important;
    }

    .mat-ripple {
        background: transparent !important;

        &::hover {
            background-color: red !important;
        }
    }

    .mat-mdc-tab-header-pagination-controls-enabled {
        .mat-mdc-tab-link-container {
            margin-left: 0px !important;
            margin-right: 0px !important;
        }
    }

    .mat-mdc-tab-link-container {
        margin-left: 20px !important;
        margin-right: 20px !important;
        width: 80% !important;
    }

    .mat-mdc-tab-link {
        min-width: 0px !important;
    }

    .mdc-tab-indicator__content--underline {
        border-bottom: none !important;
        border-width: 0px !important;
    }
}

.esg-section .custom-mat-tab .mat-mdc-tab-link-container,
.data-request-section .custom-mat-tab .mat-mdc-tab-link-container {
    margin-right: 0px !important;
}
.esg-section .custom-mat-tab .mdc-tab--active,
.data-request-section .custom-mat-tab .mdc-tab--active {
    background-color: #FFFFFF !important;
}
.esg-section .custom-mat-tab .mat-mdc-tab-link-container,
.data-request-section .custom-mat-tab .mat-mdc-tab-link-container {
    background-color: #FFFFFF !important;
}

.portfolio-company-detail-section .custom-mat-tab .mdc-tab--active {
    background-color: $nep-white !important;
}
.portfolio-company-detail-section .custom-mat-tab .mat-mdc-tab-link-container,
.portfolio-company-detail-section .custom-mat-tab .mat-mdc-tab-nav-bar.mat-mdc-tab-header {
    background-color: #FFFFFF !important;
}
.portfolio-company-detail-section .custom-mat-tab .mat-mdc-tab-nav-bar.mat-mdc-tab-header button {
    background-color: #FFFFFF !important;
    margin-bottom: 1px;
}

.custom-client-tab {
    .mat-mdc-tab-header-pagination-before {
        border-right: 1px solid $nep-nav-tab-border-color;
        background: $nep-white;
        border-bottom: 1px solid $nep-nav-tab-border-color;
        margin-right: 20px;
    }

    .mat-mdc-tab-header-pagination-after {
        border-left: 1px solid $nep-nav-tab-border-color;
        background: $nep-white;
        border-bottom: 1px solid $nep-nav-tab-border-color;
        margin-left: 20px;
    }

    .mat-mdc-tab-link {
        height: 34px !important;
        letter-spacing: 0px;
        // color: #55565A !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
        background: $nep-white !important;

        //opacity: 1;
        //font-family: Helvetica Neue LT W05_55 Roman, Arial, Verdana, Tahoma, sans-serif;
        .mdc-tab__text-label {
            letter-spacing: 0px;
            color: #55565A !important;
            opacity: 1;
            font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;
        }

        .mdc-tab-indicator {
            &::hover {
                background: transparent !important;
            }
        }
    }

    .mat-mdc-tab-link:hover {
        color: #4061C7 !important;
        background: $nep-white !important;
    }

    .mat-mdc-focus-indicator:hover,
    .mat-mdc-focus-indicator:focus,
    .mat-mdc-focus-indicator:focus-within,
    .mat-mdc-focus-indicator {
        background: transparent !important;
    }

    .mdc-tab--active:focus,
    .mdc-tab--active:focus-within {
        background: #fafafb !important;
    }

    .mat-mdc-tab-header-pagination {
        padding-left: 0px !important;
        width: 44px !important;
        min-width: 44px !important;
    }

    .mat-mdc-tab-header-pagination-controls-enabled {
        padding-top: 0px !important;

        .mat-mdc-tab-link-container {
            padding-top: 8px !important;
        }
    }

    .mdc-tab--active {
        position: relative !important;
        background-color: $nep-white !important;
        border: 1px solid $nep-nav-tab-border-color ;
        border-top-left-radius: 0.25rem;
        border-top-right-radius: 0.25rem;
        border-bottom: none !important;
        color: #4061C7 !important;
        .mdc-tab__text-label {
            color: #4061C7 !important;
            @extend .Body-R;
        }

        &::hover {
            background-color: $nep-white !important;
        }
    }

    .mat-mdc-tab-link-container {
        background-color: $nep-white !important;
        letter-spacing: 0px !important;
        opacity: 1 !important;

        &::before {
            border-top: 1px solid #dee2e6;
            content: "";
            margin: 0 auto;
            position: absolute;
            top: 97%;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            z-index: -1;
        }
    }

    .mat-ink-bar {
        display: none !important;
    }

    .mat-mdc-tab-header {
        margin-bottom: -10px !important;
        border-bottom: none !important;
        background-color: $nep-white !important;
    }

    mat-ripple {
        background: transparent !important;

        &::hover {
            background-color: red !important;
        }
    }

    .mat-mdc-tab-header-pagination-controls-enabled {
        .mat-mdc-tab-link-container {
            margin-left: 0px !important;
            margin-right: 0px !important;
        }
    }

    .mat-mdc-tab-link-container {
        margin-left: 20px !important;
        margin-right: 20px !important;
    }

    .mat-mdc-tab-link {
        min-width: 0px !important;
    }

    .mdc-tab-indicator__content--underline {
        border-bottom: none !important;
        border-width: 0px !important;
    }
}

.breadcrumb-width {
    max-width: 100%;
}

.downloadNewDealInvestments {
    .nep-modal {
        .nep-card {
            width: 19rem !important;
        }
    }
}

th {
    @extend .TextTruncate
}

.client-reporting-virtual-table {
    .p-datatable-frozen-view {
        .p-datatable-virtual-scrollable-body {
            overflow: hidden !important;
        }

        .p-datatable .p-datatable-tbody>tr:last-child>td {
            border-bottom: 1px solid #DEDFE0 !important;
        }
    }
}

.p-multiselect-panel .p-multiselect-items .p-multiselect-item {
    padding: 0.5rem 0.5rem !important;
}

.custom-reporting-bg {
    .row {
        border-top: 1px solid $nep-nav-tab-border-color;
        background: $nep-white !important;
    }
}

.fixed-footer {
    z-index: 1500 !important;
    box-shadow: 0px -3px 6px #00000014 !important;
    width:100%;
}

.client-reporting-virtual-table {
    .cdk-virtual-scroll-viewport {
        overflow-y: scroll !important;
        overflow-x: auto !important;
    }
}

.custom-formula-div {
    background: #E8EAF6 0% 0% no-repeat padding-box;
    border-radius: 4px;
    text-align: left;
    letter-spacing: 0px;
    color: #212121;
    opacity: 1;
    padding: 4px 8px;
}

.kpi-formula-btn {
    max-width: 300px;
}

.company-kpi-financial-section {
    .nep-tab-alignment {
        margin-top: 0px !important;
    }

    .nep-tabs-tab {
        padding-top: 9px !important;
        padding-bottom: 10px !important;
    }
}

.kpis-calendar {
    .p-datepicker {
        margin-top: 32px !important;
    }

    .p-calendar {
        width: 240px !important;
        background-color: #FFFFFF !important;
        border: 1px solid #CAC9C7 !important;
        border-radius: 4px !important;

        input {
            border-bottom: none !important;
            padding-right: 0px !important;
            color: #000000;
            opacity: 1 !important;
        }

        button {
            border-bottom: none !important;
            border-radius: 4px !important;
            padding-left: 8px !important;
            padding-right: 8px !important;
            width: auto !important;

            &:hover {
                background: transparent !important;
            }
        }
    }
}

p-dropdown {

    .p-disabled,
    .p-component:disabled {
        border: 1px solid #D9D9DD !important;

        .pi-chevron-down:before {
            color: #B7B8B9 !important;
        }
    }
}

.lp-report-period {
    .p-dropdown {
        width: 200px !important;
    }
}

.foot-editor-section {
    quill-editor {
        width: 100%;
    }

    .ql-toolbar {
        border-top: none !important;
        border-color: #DEDFE0 !important;
        background: $nep-white 0% 0% no-repeat padding-box;

        .ql-formats {
            margin-right: 8px !important;
        }

        .ql-picker-options {
            max-height: 150px !important;
            overflow-y: auto !important;
            overflow-x: hidden !important;
        }

        .ql-toolbar button {
            &::hover {
                background-color: #F7F8FC !important;
            }
        }
    }

    .ql-container.ql-snow {
        border-color: #DEDFE0 !important;
    }

    .ql-size,
    .ql-header,
    .ql-font {
        background: #FFFFFF 0% 0% no-repeat padding-box;
        border: 1px solid #CAC9C7;
        border-radius: 2px;
        opacity: 1;

        .ql-picker-label {
            border: none !important;
            background: white !important;
            color: #212121 !important;
        }

        .ql-picker-options {
            margin-top: 3px !important;
            border-radius: 2px !important;
        }
    }

    .ql-container {
        height: 100px !important;
        overflow-y: auto !important;
        background-color: $nep-white !important;
    }
   
    quill-editor:focus {
        outline: none;
    }

    .ql-editor ul[data-checked="false"]>li::before {
        font-size: 1.5em;
    }

    .ql-editor ul[data-checked="true"]>li::before {
        font-size: 1.5em;
    }
    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="9px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="9px"]::before {
        content: "9px";
    }
    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="8px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="8px"]::before {
        content: "8px";
    }

    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="10px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="10px"]::before {
        content: "10px";
    }

    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="10px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="10px"]::before {
        content: "10px";
    }

    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="12px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="12px"]::before {
        content: "12px";
    }

    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="14px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="14px"]::before {
        content: "14px";
    }

    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="16px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="16px"]::before {
        content: "16px";
    }

    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="18px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="18px"]::before {
        content: "18px";
    }

    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="20px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="20px"]::before {
        content: "20px";
    }

    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="22px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="22px"]::before {
        content: "22px";
    }

    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="24px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="24px"]::before {
        content: "24px";
    }

    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="26px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="26px"]::before {
        content: "26px";
    }

    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="28px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="28px"]::before {
        content: "28px";
    }

    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="30px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="30px"]::before {
        content: "30px";
    }

    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="32px"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="32px"]::before {
        content: "32px";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Helvetica"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Helvetica"]::before {
        content: "Helvetica";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Arial"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Arial"]::before {
        content: "Arial";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="TimesNewRoman"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="TimesNewRoman"]::before {
        content: "Times New Roman";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Garamond"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Garamond"]::before {
        content: "Garamond";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="PalatinoLinotype"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="PalatinoLinotype"]::before {
        content: "Palatino Linotype";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="monospace"]::before {
        content: "monospace";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="sans-serif"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="sans-serif"]::before {
        content: "sans-serif";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before {
        content: "serif";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Georgia"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Georgia"]::before {
        content: "Georgia";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Cambria"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Cambria"]::before {
        content: "Cambria";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Calibri"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Calibri"]::before {
        content: "Calibri";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Verdana"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Verdana"]::before {
        content: "Verdana";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Corbel"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Corbel"]::before {
        content: "Corbel";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="FranklinGothic"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="FranklinGothic"]::before {
        content: "Franklin Gothic";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="HelveticaMedium"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="HelveticaMedium"]::before {
        content: "Helvetica Medium";
    }

    .ql-font {
        .ql-picker-label {
            overflow: hidden !important;
            width: 106px;
        }
    }
}

.esg-foot-note-section .foot-editor-section {
    .ql-toolbar {
        border-left: none !important;
        border-right: none !important;
    }
    .ql-toolbar.ql-snow + .ql-container.ql-snow{
        border: none !important;
    }
}
.custom-quill-editor {
    .ql-color-picker {
        .ql-picker-label {
            padding-top: 0px !important;
        }
    }

    .ql-align {
        .ql-picker-label {
            padding-top: 0px !important;
        }
    }

    .foot-note {
        border-left: none !important;
        border-right: none !important;
    }

    .ql-toolbar {
        border-left: none !important;
        border-right: none !important;
    }

    .ql-container {
        border-left: none !important;
        border-bottom: none !important;
        border-right: none !important;
    }
}

.ql-container .ql-tooltip.ql-editing input[type="text"] {
    color: black;
 }

.ql-font-Helvetica {
    font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;
}

.ql-font-Arial {
    font-family: "Arial", Verdana, Tahoma, sans-serif !important;
}

.ql-font-TimesNewRoman {
    font-family: "Times New Roman", Verdana, Tahoma, sans-serif !important;
}

.ql-font-Garamond {
    font-family: "Garamond", Verdana, Tahoma, sans-serif !important;
}

.ql-font-PalatinoLinotype {
    font-family: "Palatino Linotype", Verdana, Tahoma, sans-serif !important;
}

.ql-font-Georgia {
    font-family: "Georgia", Verdana, Tahoma, sans-serif !important;
}

.ql-font-Cambria {
    font-family: "Cambria", Verdana, Tahoma, sans-serif !important;
}

.ql-font-Calibri {
    font-family: "Calibri", Verdana, Tahoma, sans-serif !important;
}

.ql-font-Verdana {
    font-family: "Verdana", Verdana, Tahoma, sans-serif !important;
}

.ql-font-HelveticaMedium {
    font-family: "Helvetica Neue LT W05_65 Medium", Arial, Verdana, Tahoma, sans-serif !important;
}

.ql-font-FranklinGothic {
    font-family: "Franklin Gothic", Verdana, Tahoma, sans-serif !important;
}
.ql-size-9px {
    font-size: 9px;
}
.ql-size-8px {
    font-size: 8px;
}

.ql-size-10px {
    font-size: 10px;
}

.ql-size-12px {
    font-size: 12px;
}

.ql-size-14px {
    font-size: 14px;
}

.ql-size-16px {
    font-size: 16px;
}

.ql-size-18px {
    font-size: 18px;
}

.ql-size-20px {
    font-size: 20px;
}

.ql-size-22px {
    font-size: 22px;
}

.ql-size-24px {
    font-size: 24px;
}

.ql-size-26px {
    font-size: 26px;
}

.ql-size-28px {
    font-size: 28px;
}

.ql-size-30px {
    font-size: 30px;
}

.ql-size-32px {
    font-size: 32px;
}

.ql-font-bold {
    font-weight: bold;
}

.ql-font-italic {
    font-style: italic;
}

.p-dropdown>.p-placeholder {
    color: $nep-text-color !important;
    opacity: 1 !important;
    font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;
}

.p-multiselect {
    .p-placeholder {
        color: $nep-text-color !important;
        opacity: 1 !important;
        font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;
    }
}

.p-calendar {
    input {
        &::placeholder {
            font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;
        }
    }
}

.mat-custom-checkbox-lp-report>label {
    margin-bottom: 3px !important;
}

.custom-quill-editor {

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {

        .ql-size-8px,
        .ql-size-10px,
        .ql-size-10px,
        .ql-size-12px,
        .ql-size-14px,
        .ql-size-16px,
        .ql-size-18px,
        .ql-size-20px,
        .ql-size-22px,
        .ql-size-24px,
        .ql-size-26px,
        .ql-size-28px,
        .ql-size-30px,
        .ql-size-32px {
            font-size: inherit !important;
        }
    }
}

.internal-report-section {
    .internal-report-action {
        .p-dropdown {
            padding-top: 0px !important;

            .p-inputtext {
                padding-top: 4px !important;
            }
        }
    }
}

.custom-copy-to-button {
    button {
        border: 1px solid #C9C9D4 !important;
    }
}

.custom-copy-to-button-active {
    button {
        background: #EFF0F9 0% 0% no-repeat padding-box !important;
        border: 1px solid #4061C7 !important;
    }
}

.custom-prefernce-button {
    button {
        padding: 8px 12px !important;

        img {
            padding-right: 8px !important;
            margin-top: -6px !important;
        }
    }
}

.internal-report-multiselect {
    .p-multiselect-header {
        .p-checkbox {
            display: none !important;
        }
    }
}

.internal-report-preference-pop-up {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 12px #00000029;
    border: 1px solid #DEDFE0;
    opacity: 1;
    width: 420px !important;
    max-width: 420px !important;
    border-radius: 4px !important;
    margin-top: 9px !important;

    .internal-period-section {
        background: #FFFFFF 0% 0% no-repeat padding-box;
        box-shadow: 0px 0px 12px #00000014;
        border: 1px solid #C9C9D4;
        border-radius: 4px;
        opacity: 1;
        padding: 12px 16px;
        width: 240px;

        .preference-tab {
            letter-spacing: 0.17px;
            color: #55565A;
            opacity: 1;
            margin-right: 30px;
            cursor: pointer;

            &:last-child {
                margin-right: 0px !important;
            }
        }

        .period-text-section {
            padding-top: 12px !important;

            .period-quarter-label {
                padding-right: 5px !important;
            }

            input {
                width: 142px;
                background: #FFFFFF 0% 0% no-repeat padding-box;
                border: 1px solid #C9C9D4;
                border-radius: 4px;
                opacity: 1;
                padding: 7px 8px !important;
            }

            .period-label {
                letter-spacing: 0px;
                color: #55565A;
                opacity: 1;
                padding-right: 12px;
                width: 64px;
            }
        }

        .preference-tab-btn-section {
            background: #FFFFFF 0% 0% no-repeat padding-box;
            border: 1px solid #DEDFE0;
            border-radius: 4px;
            opacity: 1;
            width: 100%;
            padding: 2px;

            .period-btn {
                padding: 0px 4px;
                cursor: pointer;
            }

            .period-btn-active {
                background: #F7F8FC 0% 0% no-repeat padding-box;
                border-radius: 4px;
                letter-spacing: 0px;
                color: #4061C7;
                opacity: 1;
                padding: 2px 8px;
            }

            .period-btn-in-active {
                letter-spacing: 0px;
                color: #55565A;
                opacity: 1;
            }
        }

        .preference-tab-active {
            letter-spacing: 0.17px;
            color: #4061C7;
            opacity: 1;
            border-bottom: 1px solid #4061C7;
        }
    }

    .preference-header {
        padding: 12px 12px;
        border-bottom: 1px solid #DEDFE0;

        .float-left {
            letter-spacing: 0px;
            color: #55565A;
            font-family: "Helvetica Neue LT W05_65 Medium", Arial, Verdana, Tahoma, sans-serif;
        }
    }

    .preference-content {
        height: 341px;
        border-bottom: 1px solid #DEDFE0;

    }

    .preference-label-content {
        padding: 12px 16px;
        width: 270px;
        position: absolute;

        .preference-text-label {
            text-align: left;
            letter-spacing: 0px;
            color: #55565A;
            opacity: 1;
        }
    }

    .preference-label {
        border-right: 1px solid #DEDFE0;
        height: 340px;
        width: 146px;

        .preference-section {
            letter-spacing: 0px;
            color: #55565A;
            opacity: 1;
            padding: 8px 12px 8px 12px;
            width: 100%;
            cursor: pointer;

            &:first-child {
                padding-top: 16px !important;
            }

            &:last-child {
                padding-bottom: 16px !important;
            }

            &:after {
                content: "*";
                color: red;
                // padding-left: 8px;
            }

            &:hover {
                color: #3949AB !important;
            }
        }

        .preference-active {
            color: #4061C7 !important;
            font-family: "Helvetica Neue LT W05_65 Medium", Arial, Verdana, Tahoma, sans-serif !important;
        }

        .label-req-preference {
            &:after {
                content: "" !important;
                color: transparent !important;
            }
        }
    }

    .preference-footer {
        padding: 8px 16px;

        .preference-reset-button {
            padding-right: 12px !important;

        }
    }

    .edit-mode-radio-button {
        padding-top: 2px !important;
    }

    .edit-mode-input {
        margin-top: 1px !important;
    }

    .company-group-list-padding {
        margin-top: 8px !important;
        margin-bottom: 8px !important;
    }

    .company-group-list-padding:first-child {
        margin-top: 16px !important;
        margin-bottom: 8px !important;
    }

    .company-group-list-padding:last-child {
        margin-top: 8px !important;
        margin-bottom: 16px !important;
    }
}

.access-bulkupload-p-calendar {
    .p-calendar {
        width: 240px;
        height: 32px;
        background-color: #FFFFFF !important;
        border: 1px solid #CAC9C7;
        border-radius: 4px !important;
        padding-left: 8px;

        .p-inputtext {
            border-bottom: none !important;
            padding: 0 !important;
        }

        div {
            top: 31px !important;
        }

        .p-button.p-button-icon-only {
            height: 1.813rem !important;
            border-bottom: none !important;
            width: 2.357rem !important;
        }
    }
}

.custom-table-paginator {
    .paginator-textbox {
        width: 36px !important;
        height: 30px !important;
        text-align: center;
        background: #FFFFFF 0% 0% no-repeat padding-box;
        border: 1px solid #C9C9D4;
        border-radius: 4px;
        opacity: 1;
        padding: 0 !important;
    }

    .padding-slash {
        padding: 0 4px 0 4px !important;
    }

    .end-item {
        background: #FAFAFB !important;
        padding-right: 4px !important;
        padding-top: 4px !important;
    }

    .padding-center-item {
        padding-top: 5px !important;
        padding-left: 16px !important;
    }

    .padding-right-item {
        padding-top: 5px !important;
        padding-left: 16px !important;
    }

    .text-content-style {
        letter-spacing: 0px !important;
        color: #212121 !important;
        opacity: 10px !important;
    }

    .p-paginator .p-dropdown {
        bottom: 6px !important;
        position: absolute;
        right: 12px !important;
        background: #FFFFFF 0% 0% no-repeat padding-box;
        border-bottom: none !important;
        border-radius: 4px;
        opacity: 1;
        height: 30px !important;
    }

    .p-paginator .p-dropdown .p-dropdown-label {
        padding-top: 5px !important;
        padding-left: 8px !important;
        border-left: 1px solid #DEDFE0 !important;
        border-bottom: 1px solid #DEDFE0 !important;
        border-top: 1px solid #DEDFE0 !important;
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
    }

    .p-dropdown-trigger,
    .p-autocomplete-dropdown,
    .p-multiselect-trigger {
        align-items: inherit !important;
        padding-top: 0px !important;
        padding: 8px 12px !important;
        width: 0 !important;
        font-size: 12px !important;
        border-right: 1px solid #DEDFE0 !important;
        border-bottom: 1px solid #DEDFE0 !important;
        border-top: 1px solid #DEDFE0 !important;
    }

    .p-paginator {
        text-align: left;
        font: normal normal normal 14px/18px Helvetica Neue, Arial, Verdana, Tahoma, sans-serif;
        letter-spacing: 0px;
        color: #212121 !important;
        background: #FAFAFB !important;
        opacity: 1;
        height: 44px !important;
        border-radius: 0 !important;
        padding: 0 !important;
        border-top: 1px solid #DEDFE0 !important;
    }

    .p-paginator .p-paginator-pages .p-paginator-page {
        text-align: center;
        font: normal normal normal 14px/18px Helvetica Neue, Arial, Verdana, Tahoma, sans-serif;
        letter-spacing: 0px;
        color: #75787B;
        border: none !important;
        height: 32px !important;
        width: 32px !important;
        margin: 0 2px 0 2px !important;
        opacity: 1;
        background: #FAFAFB !important;
    }

    .p-paginator .p-paginator-pages .p-paginator-page:not(.p-highlight):hover {
        height: 32px !important;
        width: 32px !important;
        background: #EFF0F9 0% 0% no-repeat padding-box !important;
        border-radius: 4px;
        opacity: 1;
        color: #4061C7 !important;
    }

    .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight):hover,
    .p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight):hover,
    .p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight):hover,
    .p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight):hover {
        height: 2rem !important;
        width: 2rem !important;
        background: #EFF0F9 0% 0% no-repeat padding-box !important;
        border-radius: 4px;
        opacity: 1;
        color: #4061C7 !important;
    }

    .p-paginator .p-paginator-first,
    .p-paginator .p-paginator-prev,
    .p-paginator .p-paginator-next,
    .p-paginator .p-paginator-last {
        min-width: 2rem !important;
        height: 2rem !important;
        margin: 0 2px 0 2px !important;
        transition: box-shadow 0.15s;
        border-radius: 0 !important;
    }

    .p-link:focus {
        box-shadow: none !important;
    }

    .p-paginator .p-paginator-first,
    .p-paginator .p-paginator-prev,
    .p-paginator .p-paginator-next,
    .p-paginator .p-paginator-last {
        border: none !important;
        color: #75787B !important;
        opacity: 1;
        background: #FAFAFB !important;
    }

    p-overlay {
        position: absolute !important;
        right: 0px !important;
        width: 52px !important;
        bottom: 31px !important;
        z-index: 1 !important;
    }

    .p-overlay {
        position: unset !important;
        min-width: 42% !important;
        max-width: 240px !important;
    }

    .p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
        background: #FFFFFF 0% 0% no-repeat padding-box !important;
        border: 1px solid #4061C7 !important;
        border-radius: 4px;
        opacity: 1;
        height: 32px !important;
        width: 32px !important;
        color: #4061C7 !important;
    }


    .p-paginator-current {
        position: absolute;
        left: 921px !important;
    }

    .custom-align-items {
        align-items: center;
    }

    .item-page-style {
        text-align: left;
        font: normal normal normal 14px/18px Helvetica Neue, Arial, Verdana, Tahoma, sans-serif;
        letter-spacing: 0px;
        color: #55565A;
        opacity: 1;
    }

    .paginator-custom-content {
        position: absolute !important;
        bottom: 14px !important;

    }

    .right-item {
        .currentpage-span {
            text-align: left;
            font: normal normal normal 14px/18px Helvetica Neue, Arial, Verdana, Tahoma, sans-serif;
            letter-spacing: 0px;
            color: #212121;
            opacity: 1;
        }

        .custom-right-style {
            text-align: center;
            font: normal normal normal 12px/14px Helvetica Neue, Arial, Verdana, Tahoma, sans-serif;
            letter-spacing: 0px;
            color: #212121;
            opacity: 1;
        }
    }

    .left-item {
        text-align: left;
        font: normal normal normal 12px/17px Helvetica Neue, Arial, Verdana, Tahoma, sans-serif;
        letter-spacing: 0px;
        color: #212121;
        opacity: 1;
        position: absolute;
        bottom: 14px !important;
        left: 12px !important;
    }

    .pi {
        font-size: 16px !important;
    }

    .p-paginator-left-content {
        margin-right: 0;
        position: absolute;
        left: 0px !important;
        min-width: 540px !important;
    }

    .p-paginator-right-content {
        position: absolute;
        right: 85px !important;

    }

    .p-dropdown-trigger .pi,
    .p-autocomplete-dropdown .pi,
    .p-multiselect-trigger .pi {
        position: absolute;
        top: 6px !important;
    }

    .p-paginator {
        .p-disabled {
            opacity: 0.4 !important;
        }
    }

    .p-dropdown-panel.p-component.ng-star-inserted {
        right: 0 !important;
        left: 58% !important;
        min-width: 42% !important;
    }
}

.template-label {
    &:after {
        content: "*";
        color: red;
    }
}

.remove-frozen {
    border: none !important;
}

.operational_KPI_Allvalues {
    float: left;
    font-size: 12px;
    margin-right: 12px;
    color: $nep-icon-grey;
    padding-left: 16px !important;
    padding-top: 8px !important;
}

.ir-company-no-data {
    .row {
        margin-top: 150px !important;
    }
}

.internal-kpi-footer {
    border-top: 1px solid #DEDFE0 !important;
    position: absolute;
    bottom: 0px;
    background-color: #FAFAFA !important;
    border-radius: 0px 0px 4px 0px;

    .footer-section {
        padding-right: 12px;
    }

    button {
        padding-top: 3px !important;
        margin-top: -2px;

    }

    .copy-button {
        padding-left: 12px;
    }
}

.OperationalKPI {
    .p-datatable-frozen-view>.p-datatable-scrollable-body>table>.p-datatable-tbody>tr>td:first-child {
        padding-left: 16px !important;
        padding-right: 16px !important;
    }
}

@media only screen and (min-width: 760px) and (max-width: 1023px) {
    .custom-fund-left-width {
        max-width: 65% !important;
    }
}

@media only screen and (min-width: 1024px) and (max-width: 1151px) {
    .custom-fund-left-width {
        max-width: 65% !important;
    }

    .custom-header-left-width {
        max-width: 70% !important;
    }

    .top-left-box {
        width: 76% !important;
    }

    .top-right-box {
        width: 22% !important;
    }
}

@media only screen and (min-width: 1366px) and (max-width: 1440px) {
    .custom-fund-left-width {
        max-width: 76% !important;
    }

    .custom-header-left-width {
        max-width: 70% !important;
    }

    .top-left-box {
        width: 82% !important;
    }

    .top-right-box {
        width: 16% !important;
    }
}

@media only screen and (min-width: 1441px) and (max-width: 1791px) {
    .custom-fund-left-width {
        max-width: 77% !important;
    }

    .custom-header-left-width {
        max-width: 70% !important;
    }
}

@media only screen and (min-width: 1800px) {
    .custom-fund-left-width {
        max-width: 84% !important;
    }

    .custom-header-left-width {
        max-width: 70% !important;
    }
}

.OperationalKPISearchBox {
    padding-right: 38px !important;
}

.error-period-type {
    color: #75787B;
    letter-spacing: 0px;
    opacity: 1;
}

.internal-select {
    .p-dropdown-label {
        width: 80% !important;
    }
}

.section-tick {
    float: right;
    margin-top: 5px;
}

.internal-report-tbl {
    .cdk-virtual-scroll-viewport {
        overflow-y: scroll !important;
    }
}

.myCustomModalClass {
    .modal-dialog {
        top: 35% !important;
        width: 489px !important;
    }

    .modal-content {
        border-radius: 4px !important;
        border: 1px solid #e0e0e0 !important;
    }

    .nep-modal-title {
        border-top-left-radius: 4px !important;
        border-top-right-radius: 4px !important;
    }

    .nep-card-footer {
        border-bottom-left-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
    }

    .modal-body {
        padding: 20px !important;
    }

    p {
        margin: 0 !important;
    }

    .nep-modal-title {
        font-weight: 600 !important;
    }

    background-color: rgba(0, 0, 0, 0.4);
}

.internal-report-calendar {
    height: 32px;

    .date-picker-input {
        border: 1px solid #C9C9D4 !important;
        border-right: none !important;
        border-top-left-radius: 4px !important;
        border-bottom-left-radius: 4px !important;
        padding-left: 12px !important;
    }

    .p-button {
        height: 32px !important;
        border-left: none !important;
        border: 1px solid #C9C9D4;
        border-top-right-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
    }

    .pi-calendar {
        font-size: 0.9rem !important;
        padding-top: 2px !important;
    }

    .p-monthpicker-month {
        border-radius: 0px !important;
    }

    .p-highlight {
        color: #4061C7 !important;
        background: #E8EAF6 !important;
    }

}

.download-calendar {
    width: 240px !important;

    .error-input {
        .p-calendar {
            border: 1px solid #C62828 !important;
        }
    }
}

.nep-modal-header {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.nep-modal-header1 {
    position: relative !important;
    display: inline-flex !important;
    top: 35% !important;
}

.nep-modal-header3 {
    display: flex !important;
}

//for deal-details component.html

.deal-detail-header-h {
    height: 30px !important;
}

.deal-detail-header-b {
    border: none !important;
}

.deal-detail-header-m {
    margin: 0 !important;
}

.deal-detail-header-b1 {
    border: none !important;
}

// portfolio-fundHolding.component.html
.fundholding-d-bg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.fundholding-p-d {
    position: relative !important;
    display: inline-flex !important;
}
//save-deals.component.html
.save-deals-pb {
    padding-bottom: 5.2px !important;
}

//LpTemplateConfiguration.component.html

.LpTemplate-bs {
    box-shadow: 0px 3px 6px #00000014 !important;
}

.LpTemplate-bs1 {
    box-shadow: 0px 3px 6px #00000014 !important;
}

.LpTemplate-d {
    display: none !important;
}

///advance-filters.component.html
.advancefilters-w-h {
    width: 478px !important;
    height: 420px !important;
}

.advancefilters-hw-p {
    height: 49px !important;
    width: 478px !important;
    padding: 15px 36px 16px 17px !important;
}

.advancefilters-fs-f {
    font-size: 16px !important;
    float: left !important;
}

.advancefilters-fs-fc {
    font-size: 14px !important;
    float: right !important;
    color: #4061C7 !important;
    cursor: pointer !important;
}

.advancefilters-bb-bt {
    border-bottom: 1px solid #f0f0f1 !important;
    border-top: 1px solid #f0f0f1 !important;
    height: 314px !important;
    width: 478px !important;
}

.advancefilters-d {
    display: flex !important;
}

.advancefilters-w-bg {
    width: 160px !important;
    background-color: #FAFAFB !important;
    height: 314px !important;
    border-bottom: 1px solid #f0f0f1 !important;
}

.advancefilters-height {
    width: 338px !important;
    height: 314px !important;
}

.advancefilters-pt-pl {
    padding-top: 20px !important;
    padding-left: 20px !important;
}

.advancefilters-w-mt-ml {
    width: 252px !important;
    margin-top: 20px !important;
    margin-left: 20px !important;
}

.advancefilters-c-pl {
    color: #c62828 !important;
    padding-left: 0.5rem !important;
}

.advancefilters-margintop {
    width: 252px !important;
    margin-top: 40px !important;
    margin-left: 20px !important;
}

.advancefilters-cp {
    color: #c62828 !important;
    padding-left: 0.5rem !important;
}

.advancefilters-w-mt {
    width: 280px !important;
    margin-left: 20px !important;
    margin-top: 20px !important;
}

.contentstyle-mb {
    margin-bottom: 3px !important;
}

.contentstyle-marginbottom {
    margin-bottom: 3px !important;
}

.contentstyle-bottom-margin {
    margin-bottom: 3px !important;
}

.contentstyle-bm {
    margin-bottom: 3px !important;
}

.containerfooter-hw {
    height: 57px !important;
    width: 478px !important;
}

.advancefilters-fr-p {
    float: right !important;
    padding: 12px 20px 12px 20px !important;
}


//login.component.html
.login-mt {
    margin-top: 8rem !important;
}

.card-pt-pb {
    padding-top: 40px !important;
    padding-bottom: 40px !important;
    border-radius: 0.5rem !important;
    padding-left: 20px !important;
    padding-right: 20px !important;
}

.logincol-mt {
    margin-top: 2rem !important;
}


//cashflow-list.component.html
.cashflowlist-d {
    display: none !important;
}

.cashflowlist-d1 {
    display: none !important;
}

//cashflow-upload.component.html
.nepshow-d-bg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.nepshadow-po-d {
    position: relative !important;
    display: inline-flex !important;
    top: 35% !important;
    width: 489px !important;
}

.nepbt-p {
    padding-left: 12px !important;
}

.nepbt-pl {
    padding-left: 12px !important;
}

///cashflow.component.html
.cashflowcol-p {
    padding: 0px !important;
}

.cash-col-p {
    padding: 0px !important;
}

.cashflow-padding {
    padding: 0px !important;
}

.cashflow-pad {
    padding: 0px !important;
}

///client-report-tab.html
.nepbtn-td {
    text-decoration: none;
}

.nepbtn-text {
    text-decoration: none;
}

.nepbtn-text-decoration {
    text-decoration: none;
}

//consolidated-report.component.html
.consolidated-z-d {
    z-index: -1 !important;
    display: flex !important;
    width: 100% !important;
    justify-content: space-between !important;
    height: 20px !important;
}

.consolidated-display {
    z-index: 1000 !important;
    display: flex !important;
}

.consolidated-width {
    width: 60px !important;
}

//comment-control.component.html
.comment-r {
    resize: none !important;
}

//customselect.component.html
.customselect-mt-t {
    margin-top: 0px !important;
    transform: translate(0px, 0px) !important;
}

//customselect-ext.component.html
.custom-ext-mt {
    margin-top: 0px !important;
    transform: translate(0px, 0px) !important;
}

//filter-control.component.html
.filterrow-pl-pr {
    padding-left: 20px !important;
    padding-right: 20px !important;
    padding-top: 20px !important;
    padding-bottom: 20px !important;
}

.colfilter-pr-pl {
    padding-right: 0% !important;
    padding-left: 0px !important;
}

.nepfilter-pt-fs {
    padding-top: 10px !important;
    font-size: 14px !important;
    font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;
}

.col-9-pl-pr {
    padding-left: 0% !important;
    padding-right: 0% !important;
}

.filtercontrol-pt-pl {
    padding-top: 2px !important;
    padding-left: 4px !important;
}

.filterbtn-pl-pr {
    padding-left: 0% !important;
    padding-right: 0% !important;
}

.filternep-btn-pl-pr {
    padding-left: 4px !important;
    padding-right: 0% !important;
}

.nepbtn-padding-right {
    padding-right: 20px !important;
}

.nepbtn-control-pr {
    padding-right: 20px !important;
}

.neplabel-ff {
    font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;
}

.nepcontrol-pt {
    padding-top: 2px !important;
    padding-left: 4px !important;
}

//horizontal-navbar.component.html
.horizontal-mw {
    max-width: 80vw !important;
}

.horizontal-bt {
    text-decoration: none;
}

//portfolioCompany-DataExtraction.component.html
.protofilo-mh {
    min-height: 80vh !important;
}

.input-pl-pr {
    padding-left: 10px !important;
    padding-right: 10px !important;
}

//bulk-upload.component.html
.bulk-pt-pl {
    padding-top: 2px !important;
    padding-left: 4px !important;
}

.nep-d-bg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.nep-bulk-pdt {
    position: relative !important;
    display: inline-flex !important;
    top: 35% !important;
    width: 489px !important;
}

//add-firm.component.html
.fund-header-pl {
    padding-left: 0px !important;
}

.formgroup-fl {
    float: left !important;
}

.formgrup-fr {
    float: right !important;
}

.form-firm-fl {
    float: right !important;
}

//firm-details.component.html
.rowfirm-h {
    height: 30px !important;
}

.firmdetail-h {
    height: 30px !important;
}

///fund-report.component.html
.fundfilter-wh {
    width: 474px !important;
    height: 54.5vh !important;
}

.containerheader-h-p {
    height: 49px !important;
    padding: 15px 20px 16px 17px !important;
}

.fundreport-fl-c {
    float: left !important;
    color: #55565a !important;
}

.fund-fr-cp {
    float: right !important;
    cursor: pointer !important;
}

.containerBody-bb-bt {
    border-bottom: 1px solid #f0f0f1 !important;
    border-top: 1px solid #f0f0f1 !important;
}

.fund-bb-bg {
    width: 150px !important;
    background-color: #FAFAFB !important;
    border-bottom: 1px solid #f0f0f1 !important;
}

.fund-d {
    display: flex !important;
}

.fund-height {
    height: 29vh !important;
}

.fundrep-w-mt-ml {
    width: 252px !important;
    margin-top: 20px !important;
    margin-left: 20px !important;
}

.fund-ml-mt {
    width: 280px !important;
    margin-left: 20px !important;
    margin-top: 20px !important;
}

.content-margin-b {
    margin-bottom: 3px !important;
}

.contentsty-mb {
    margin-bottom: 3px !important;
}

.contstyle-mb {
    margin-bottom: 3px !important;
}

.container-fr-p {
    float: right !important;
    padding: 12px 20px 12px 20px !important;
}

.nep-butn-pl {
    padding-left: 12px !important;
}

//fund-details.component.html
.fund-detail-wp-v {
    width: 1280px !important;
    position: fixed !important;
    visibility: hidden !important;
}

.funddetails-btn {
    position: relative !important;
}

.fund-details-w-di {
    width: 100% !important;
    display: inline-block !important;
}

.pdf-border-bb {
    border-bottom: 3px solid #f26524 !important;
}

.fund-detail-fw-bc {
    font-weight: bold !important;
    color: #212121 !important;
}

.fund-fontw-bc {
    font-weight: normal !important;
    color: #212121 !important;
}

.fund-det-fw-bcolor {
    font-weight: bold !important;
    color: #212121 !important;
}

.fund-detail-fweight-boldc {
    font-weight: normal !important;
    color: #212121 !important;
}

.detail-fund-bc-fw {
    font-weight: bold !important;
    color: #212121 !important;
}

.detail-fund-bcolor-fontw {
    font-weight: normal !important;
    color: #212121 !important;
}

.detail-fund-fw-bc {
    font-weight: bold !important;
    color: #212121 !important;
}

.detail.fund-boldc-fweight {
    font-weight: normal !important;
    color: #212121 !important;
}

.fun-det-dw {
    display: inline-block !important;
    width: 100% !important;
}

.roun-cor-di {
    display: inline-block !important;
}

.graptitle-p-fs {
    padding: 16px 0 20px 16px !important;
    font-size: 24px !important;
}

.another-d-ib {
    display: inline-block !important;
}

.titgraphs-p-fs {
    padding: 16px 0 16px 16px !important;
    font-size: 24px !important;
}

.fundet-d-ib-w {
    display: inline-block !important;
    width: 100% !important;
}

.roucor-dib {
    display: inline-block !important;
}

.grptil-pfs {
    padding: 16px 0 16px 16px !important;
    font-size: 24px !important;
}

.atrside-d-ib {
    display: inline-block !important;
}

.grp-fund-pfs {
    padding: 16px 0 16px 16px !important;
    font-size: 24px !important;
}

.fud-dets-dw {
    display: inline-block !important;
    width: 100% !important;
}

.grptits-pfs {
    padding: 16px 0 16px 16px !important;
    font-size: 24px !important;
}

.fundts-rou-dib {
    display: inline-block !important;
}

.as-rc-d {
    display: inline-block !important;
}

.gt-fund-pfs {
    padding: 16px 0 16px 16px !important;
    font-size: 24px !important;
}

//fund-list.component.html
.fundl-cd {
    cursor: default !important;
}

.flist-cd {
    cursor: default !important;
}

.fundlist-cd {
    cursor: default !important;
}

.fl-cursord {
    cursor: default !important;
}

//fund-trackRecord.component.html
.fund-nep-dbg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;

}

.nepc-fd-pr-d {
    position: relative !important;
    display: inline-flex !important;
    top: 35% !important;
    width: 489px !important;
}

.col-fund-d {
    display: block !important;
}

//trackrecords.component.html
.ui-bb {
    border-bottom: 0 none !important;
}

.ui-track-bb {
    border-bottom: 0 none !important;
}

//fxrates.component.html
.fixrate-w {
    width: 200px !important
}

//counter-card.component.html
.cardheadb-oh {
    overflow: hidden !important;
}

//htmltopdf.component.html
.htmlpdf-pr {
    position: relative !important;
}

//impactupload.component.html
.rowimp-h {
    height: 12.5rem !important;
}

// .hari
// {
//     already one text center
// }
.impac-cp {
    cursor: pointer !important;
}

.card-db {
    display: block !important;
}

.impupload-w {
    width: 24px !important;
}

//internal-report.component.html
.cus-ui-z-d-mw {
    z-index: -1 !important;
    display: flex !important;
    width: 100% !important;
    justify-content: space-between !important;
    height: 20px !important;
}

.custom-internal-zd {
    z-index: 1000 !important;
    display: flex !important;
}

.cstm-int-wh {
    width: 60px !important;
    height: 34px !important;
}

//top-holding-investor
.cus-ui-topinvs-dele-z-d-mw {
    z-index: -1 !important;
    display: flex !important;
    width: 100% !important;
    justify-content: space-between !important;
    height: 20px !important;
}

.custom-internal-top-invest-delete {
    z-index: 1000 !important;
    display: flex !important;
}

//investorDetails.component.html
.ui-wid-bb {
    border-bottom: 0 none !important;
}

//kpi-formula-builder.component.html
.nep-mdl-shw-d-bg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.nep-crd-pr-d {
    position: relative !important;
    display: inline-flex !important;
}

//kpi-list.component.html
.nep-kpi-bg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.nep-kpilist-pr {
    position: relative !important;
    display: inline-flex !important;
}

.list-kpi-h-o {
    height: auto !important;
    overflow: auto !important;
}

.kpinep-butn-pl {
    padding-left: 12px !important;
}

//portfolio-company-mapping/portfolio-company-mapping.component.html
.protofilo-cmpny-o {
    overflow-y: auto !important;
}

.procmpny-of {
    overflow-y: scroll !important;
}

//zero-state-kpi.component.html
.zerokpi-mt-ml {
    height: calc(100vh - 300px) !important;
    display: flex;
    flex-direction: row;
    align-items: center;
    text-align: center;
}

.zerkpi-hw-ml-mt {
    height: 30px !important;
    width: 25px !important;
    margin-left: 5% !important;
    margin-top: 5% !important;
}

.zero-kpi-stat--width {
    width: 100% !important;
}

.zero-kpi-stat-mt {
    margin-top: 20px !important;
}

//lp-report-config.component.html
.lp-rept-margin {
    margin: 0px !important;
}

.lp-report-br-h {
    border-radius: 0px !important;
    height: 42px !important;
    border-left: none !important;
}

.lprep-mt-mr {
    margin-top: -32px !important;
    margin-right: 5px !important;
}

.row-lp-of {
    overflow-y: scroll !important;
}

.lp-repconfig-ls-c {
    letter-spacing: 0px !important;
    color: #55565A !important;
}

.lp-row-fw {
    font-weight: bold !important;
}

.lpreport-backgrnd {
    background-color: #fff !important;
}

.lp-feed-mt-di {
    margin-top: 0px !important;
    display: inline-block !important;
}

.lprepform-wh-br-bb {
    width: 339px !important;
    height: 42px !important;
    border-radius: 4px 0px 0px 0px !important;
    border-bottom: none !important;
    border-left: none !important;
    border-top: none !important;
}

.lp-floatr-di-p {
    display: inline-block !important;
    padding: 11px !important;
}

.lp-row-backg {
    background-color: #fff !important;
}

.report-lp-bg {
    background-color: #fff !important;
}

.txtlprpt-w-bg-pl {
    width: 200px !important;
    background-color: #fff !important;
    padding-left: 12px !important;
    text-align: left !important;
}

.rptlp-bgc {
    background-color: transparent !important;
}

.rptlptxt-pl-bg-w {
    width: 200px !important;
    background-color: #fff !important;
    padding-left: 12px !important;
}

.rptlpbg-bg {
    background-color: #fff !important;
}

.texrprt-elps-ow-pl {
    outline: none !important;
    width: 200px !important;
    padding-left: 12px !important;
}

.textrprt-br {
    border-radius: 4px !important;
}

//breadcrumb.component.html
.bread-m-r {
    margin-right: 8px !important;
}

//header.component.html

.mark-tooltip-d {
    display: none !important;
}

.rowhead-mt {
    margin-top: -2px !important;
}

.txt-head-mw {
    max-width: 120px !important;
}

//master.component.html
.mat-mast-c {
    color: #fff !important;
}

.mster-pf-b {
    position: fixed !important;
    bottom: 0 !important;
}

.side-mstr-p {
    position: relative !important;
}
.butn-fr {
    float: right !important;
}

//add-pipeline.component.html
.add-pipe-mh {
    min-height: 100vh !important;
}

.pipe-add-bb {
    border-bottom: none !important;
}

//popular-tags.component.html
.pop-mar-t {
    margin-top: 75px !important;
}

//add-portfolioCompany.component.html
.row-ad-d {
    display: none !important;
}

///balance-sheet.component.html
.blnc-w {
    width: 200px !important;
}

//cash-flow.component.html
.cash-flw-w {
    width: 200px !important;
}

//profit-and-loss.component.html no 68 not mention
.prft-loss-w {
    width: 200px !important;
}

//master-kpi.component.html
.mstkpi-o {
    outline: none !important;
}

.col-kpi-fs {
    font-size: 14px !important;
    line-height: 24px !important;
}

.col-fs-lh-p {
    color: #E15400 !important;
    font-size: 12px !important;
    line-height: 14px !important;
    padding: 6px !important;
}

.row-kpi-mt {
    margin-top: 20px !important;
}

.mstr-kpi-c-fs-lh {
    color: #55565a !important;
    font-size: 14px !important;
    line-height: 20px !important;
}

.mastr-kpi-clr {
    color: #000000 !important;
}

.kpi-mstr-mt-fs-lh {
    margin-top: 20px !important;
    font-size: 14px !important;
    line-height: 24px !important;
}

.mtr-kpi-lh {
    line-height: 24px !important;
}

//portfolioCompany-detail.operational-KPI.html
.col-prtfcmpny-w {
    width: 200px !important;
}

.prtcmny-det-o {
    outline: none !important;
}

.col-det-fs-lh {
    font-size: 14px !important;
    line-height: 24px !important;
}

.col-txt-c-fs-lh {
    color: #E15400 !important;
    font-size: 12px !important;
    line-height: 14px !important;
    padding: 6px !important;
}

.row-mt {
    margin-top: 20px !important;
}

.portfol-fs-clr {
    color: #55565a !important;
    font-size: 14px !important;
    line-height: 20px !important;
}

.portfol-clr {
    color: #000000 !important;
}

.port-oper-kpi-mt-fs {
    margin-top: 20px !important;
    font-size: 14px !important;
    line-height: 24px !important;
}

.oper-kpi-lh {
    line-height: 24px !important;
}

//portfolioCompany-CompanyKPI.component.html
.companykpi-w {
    width: 200px !important;
}

.companykpi-o {
    outline: none !important;
}

.col-operationkpi-fs-lh {
    font-size: 14px !important;
    line-height: 24px !important;
}

.col-oprkpi-clr-fs {
    color: #E15400 !important;
    font-size: 12px !important;
    line-height: 14px !important;
    padding: 6px !important;
}

.row-oprkpi-mt {
    margin-top: 20px !important;
}

.opertkpi-clr-fs-lh {
    color: #55565a !important;
    font-size: 14px !important;
    line-height: 20px !important;
}

.operkpi-clr {
    color: #000000 !important;
}

.portfolio-kpi-mt-fs-lh {
    margin-top: 20px !important;
    font-size: 14px !important;
    line-height: 24px !important;
}

.oprtnkpi-lh {
    line-height: 24px !important;
}

///portfolioCompany-ImpactKPI.component.html
.impactkpi-w {
    width: 200px !important;
}

.impakpi-h {
    height: 44px !important;
}

.col-impact-fs-lh {
    font-size: 14px !important;
    line-height: 24px !important;
}

.col-impactkpi-clr-fs {
    color: #E15400 !important;
    font-size: 12px !important;
    line-height: 14px !important;
    padding: 6px !important;
}

.row-impkpi-mt {
    margin-top: 20px !important;
}

.impactkpi-clr-fs-lh {
    color: #55565a !important;
    font-size: 14px !important;
    line-height: 20px !important;
}

.imptkpi-colr {
    color: #000000 !important;
}

.impctkpi-mt-fs-lh {
    margin-top: 20px !important;
    font-size: 14px !important;
    line-height: 24px !important;
}

.company-impact-lh {
    line-height: 24px !important;
}

//portfolioCompany-InvestmentKPI.component.html
.investkpi-o {
    outline: none !important;
}

.col-investkpi-fs-lh {
    font-size: 14px !important;
    line-height: 24px !important;
}

.col-invstkpi-clr-fs-lh {
    color: #E15400 !important;
    font-size: 12px !important;
    line-height: 14px !important;
    padding: 6px !important;
}

.row-investkpi-mt {
    margin-top: 20px !important;
}

.investmentkpi-clr-fs-lh {
    color: #55565a !important;
    font-size: 14px !important;
    line-height: 20px !important;
}

.invtmtkpi-clr {
    color: #000000 !important
}

.compny-invst-mt-fs-lh {
    margin-top: 20px !important;
    font-size: 14px !important;
    line-height: 24px !important;
}

.invskpi-lh {
    line-height: 24px !important;
}

//portfolioCompany-detail.component.html
.detail-w-p-v {
    width: 1280px !important;
    position: fixed !important;
    visibility: hidden !important;
}

///published.component.html
.float-cur {
    cursor: default !important;
}

.nepmdl-d-bg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.nepcrd-p-d-t {
    position: relative !important;
    display: inline-flex !important;
    top: 35%;
    width: 489px !important;
}

.txt-trunc-pl {
    padding-left: 12px !important;
}

//update-info-section.component.html
.modal-tlt-clr {
    color: rgba(0, 0, 0, 0.85) !important;
}

.row-updt-p {
    padding: 20px !important;
}

//view-pc-aduitlogs.component.html
.row-pc-mt {
    margin-top: 10px !important;
}

.style-clr {
    color: #E15400 !important;
}

.view-pc-mt {
    margin-top: 12px !important;
}

.col-pc-fs-lh {
    font-size: 14px !important;
    line-height: 24px !important;
}

.col-view-pc-clr-fs {
    color: #E15400 !important;
    font-size: 12px !important;
    line-height: 14px !important;
}

.row-view-mt {
    margin-top: 20px !important;
}

.view-pc-clr-fs-lh {
    color: #55565a !important;
    font-size: 14px !important;
    line-height: 20px !important;
}

.aduit-pc-clr {
    color: #000000 !important;
}

.view-audit-mt-fs {
    margin-top: 20px !important;
    font-size: 14px !important;
    line-height: 24px !important;
}

.body-inter-h {
    height: calc(100vh - 328px) !important;
}

.inter-rept-w {
    width: 60px !important;
}

.crd-img-w {
    width: 100% !important;
}

.nepbutton-td {
    text-decoration: none !important;
}

.chrt-area-bn {
    border: none !important;
}

.float-rprt-fr {
    float: right !important;
}

.excel-load-marg {
    margin: 0 !important;
}

.neplbl-rprt-ff {
    font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif !important;
}

.atrbtn-report-pt-pl {
    padding-top: 2px !important;
    padding-left: 4px !important;
}

.cur-tophold-mw {
    max-width: 90% !important;
}

.fa-hold-value-clr-fs {
    color: #d60707 !important;
    font-size: 18px !important;
}

.nepbutn-home-pl {
    padding-left: 8px !important;
}

.add-repos-bor {
    border: 4px !important;
}

.add-repos-heigh {
    height: 28px !important;
}

.row-add-rep-p {
    padding: 20px !important;
}

.repos-add-pl {
    padding-left: 12px !important;
}

.nep-add-repos-pl {
    padding-left: 8px !important;
}

.repostry-add-pl {
    padding-left: 12px !important;
}

.col-add-repos-p-mt-bl {
    padding: 20px !important;
    margin-top: 2px !important;
    border-left: 1px solid #f2f2f2 !important;
    padding-left: 30px !important;
}

.rep-ad-cur {
    cursor: pointer !important;
}

.apply-repos-f {
    float: right !important;
}

.info-rep-cur {
    cursor: default !important;
}

.red-add-pl {
    padding-left: 16px !important;
}

.row-opendoc-mr-p-mt {
    margin-right: -35px !important;
    padding: 20px !important;
    margin-top: -15px !important;
}

.toast-d-jc {
    display: flex !important;
    justify-content: center !important;
}

.col-opn-p {
    padding: 8px !important;
}

.col-doc-pr {
    padding-right: 20px !important;
}

.opn-doc-pad {
    padding: 8px !important;
}

.doc-opn-width {
    width: 110.6% !important;
}

.edit-doc-ml {
    margin-left: 20px !important;
}

.document-pl {
    padding-left: 6% !important;
}

.nep-err-mt-ml {
    margin-top: 24% !important;
    margin-left: 23% !important;
}

.txt-fnt-ml {
    margin-left: 20px !important;
}

.txt-doc-ml {
    margin-left: 20px !important;
}

.txt-opn-ml-mt {
    margin-left: 20px !important;
    margin-top: -1% !important;
}

.row-opn-mb {
    margin-bottom: 3.5% !important;
}

.col-docopn-br {
    border-right: 1px solid #dedfd0 !important;
}

.opn-doc-df {
    display: flex !important;
}

.doc-open-wid {
    width: 50% !important;
}

.documnt-opn-w-pt {
    width: 15% !important;
    padding-top: 0.3vw !important;
}

.opndoc-wid {
    width: 15% !important;
}

.docmn-w-pt {
    width: 10% !important;
    padding-top: 0.3vw !important;
}

.opendocm-wid-pt {
    width: 15% !important;
    padding-top: 0.3vw !important;
}

.w100-fw-mr {
    flex-wrap: wrap !important;
    margin-right: -15px !important;
    margin-left: -15px !important;
}

.repos-h-pt-pb {
    height: 36px !important;
    font-size: 14px !important;
    padding-top: 9px;
    padding-bottom: 9px !important;
    padding-left: 12px !important;
}

.repos-li-mt-mr {
    margin-top: 30px !important;
    margin-right: 20px !important;
}

.rep-txt-pt-pb {
    padding-top: 25px !important;
    padding-bottom: 10px !important;
}

.doc-rep-ml {
    margin-left: -15px !important;
}

.shwrest-rep-p {
    padding: 0px !important;
}

.rep-sel-w-mr {
    width: 99px !important;
    margin-right: -17px !important;
}

.replis-clr {
    color: #55565a !important;
}

.repositry-ml-b-pl {
    margin-left: 0% !important;
    border: 1px solid #DEDFE0 !important;
    padding-left: 5% !important;
    width: 98.2% !important;
    height: 78% !important;
}

.neprep-btn {
    width: 162px !important;
}

.nav-rep-pt {
    padding-top: 0px !important;
}

.nep-bulk-d-bg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.nepwrk-pr-d-t-w {
    position: relative !important;
    display: inline-flex !important;
    top: 35% !important;
    width: 489px !important;
}

.nep-bulk-btn-pl {
    padding-left: 12px !important;
}

.wrkflw-wid {
    width: 200px !important;
}

.workflw-out {
    outline: none !important;
}

.col-wrkflow-fs-lh {
    font-size: 14px !important;
    line-height: 24px !important;
}

.col-sty-wrk-clr-fs {
    color: #E15400 !important;
    font-size: 12px !important;
    line-height: 14px !important;
    padding: 6px !important;
}

.row-flow-mt {
    margin-top: 20px !important;
}

.wrkflow-cmpny-clr-fs-lh {
    color: #55565a !important;
    font-size: 14px !important;
    line-height: 20px !important;
}

.workflw-sty-clr {
    color: #000000 !important;
}

.workkpi-mt-fs-lh {
    margin-top: 20px !important;
    font-size: 14px !important;
    line-height: 24px !important;
}

.kpi-flow-lh {
    line-height: 24px !important;
}

.nep-user-db-bg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.nepadd-pr-d-t {
    position: relative !important;
    display: inline-flex !important;
    top: 35% !important;
    width: 489px !important;
}

.add-use-mt {
    margin-top: 0px !important;
}

.user-add-pl {
    padding-left: 12px !important;
}

.usr-wrk-bb {
    border-bottom: none !important;
}

.user-wflow-w-pad {
    width: 60px !important;
    padding: 20px 0px 20px 8px !important;
}

.flow-user-wid-p {
    width: 60px !important;
    padding: 0px !important;
}

.invstflow-lh {
    line-height: 24px !important;
}

.kpiwrk-out {
    outline: none !important;
}

.col-kpi-flw-fs-lh {
    font-size: 14px !important;
    line-height: 24px !important;
}

.coltxt-kpi-clr-fs {
    color: #E15400 !important;
    font-size: 12px !important;
    line-height: 14px !important;
    padding: 6px !important;
}

.rowkpi-wrk-mt {
    margin-top: 20px !important;
}

.wrk-inv-kpi-clr-fs-lh {
    color: #55565a !important;
    font-size: 14px !important;
    line-height: 20px !important;
}

.invflw-clr {
    color: #000000 !important;
}

.kpiinvs-lh {
    line-height: 24px !important;
}

.nep-cus-inv-bg-d {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.nep-crd-inv-pr-d-t {
    position: relative !important;
    display: inline-flex !important;
    top: 18% !important;
}

.work-flow-kpi-mt-fs-lk-h {
    margin-top: 20px !important;
    font-size: 14px !important;
    line-height: 24px !important;
}

.edit-kpi-cur {
    cursor: not-allowed !important;
}

.oper-kpi-wid {
    width: 200px !important;
}

.wrk-oper-out {
    outline: none !important;
}

.col-oper-kpi-fs {
    font-size: 14px !important;
    line-height: 24px !important;
}

.col-work-oper-clr-fs {
    color: #E15400 !important;
    font-size: 12px !important;
    line-height: 14px !important;
    padding: 6px !important;
}

.row-wrk-oper-mt {
    margin-top: 20px !important;
}

.oper-work-flow-clr-fs-lh {
    color: #55565a !important;
    font-size: 14px !important;
    line-height: 20px !important;
}

.operation-kpi-clr {
    color: #000000 !important;
}

.workfow-kpi-mt-fs {
    margin-top: 20px !important;
    font-size: 14px !important;
    line-height: 24px !important;
}

.kpi-operwork-lh {
    line-height: 24px !important;
}

.financials-ContainerPadding {
    .border-top {
        .filterContainerPadding {
            height: 40px !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }
    }
}
.internal-report-mapping:last-child {
    border-bottom: 1px solid #D5D5D5 !important;
}

//hari code smells -2

//deal-details.component.html

.deal-float-r {
    float: right !important;
}

//LpTemplateConfiguration.component.html

.lptemconfig-box-shdw {
    box-shadow: 0px 3px 6px #00000014 !important;
}

//advance-filters.component.html

.advnc-btn-pl {
    padding-left: 12px !important;
}

//filter-control.component.html

.fil-con-col-txt-align {
    text-align: right;
}

//firm-details.component.html

.firmdet-row-h {
    height: 30px !important;
}

//fund-report.component.html

.fund-constyle-mb {
    margin-bottom: 3px !important;
}

.fundrpt-contentsty-mb {
    margin-bottom: 3px !important;
}

//fund-details.component.html

.fundet-rou-dsply {
    display: inline-block !important;
}

//trackrecords.component.html

.ui-wed-bb {
    border-bottom: 0 none !important;
}

//lp-report-config.component.html

.lp-rpt-mt-mr {
    margin-top: -32px !important;
    margin-right: 12px !important;
}

//popular-tags.component.html

.poplr-mt {
    margin-top: 75px !important;
}

//balance-sheet.component.html

.blc-sheet-w {
    width: 200px !important;
}

//cash-flow.component.html

.cash-flw-wid {
    width: 200px !important;
}

//add-repository.component.html

.nep-mdl-dsply-bg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.mdl-nep-dflt-pr-dif {
    position: relative !important;
    display: inline-flex !important;
}

.add-rep-cur-point {
    cursor: pointer !important;
}

.aplyred-fr {
    float: right !important;
}

.info-style-cur {
    cursor: default !important;
}

.aply-clr-pad-lft {
    padding-left: 16px !important;
}

.frmcntr-resizeNo {
    resize: none !important;
}

//open-document.component.html

.opn-doc-wid {
    width: 100% !important;
}

.opn-doc-mar-tp {
    margin-top: -1% !important;
}

.doc-w100-flxwrp {
    flex-wrap: wrap !important;
}

//repository-list.component.html

.rep-list-h-pt-fs-pb {
    height: 36px !important;
    font-size: 14px !important;
    padding-top: 9px !important;
    padding-bottom: 9px !important;
    padding-left: 12px !important;
}

.repstry-list-padding {
    padding: 1em 1.5em !important;
}

//workflow-companyinfo.component.html

.custm-mdl-db-bg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.nep-wrkflw-pr-disply {
    position: relative !important;
    display: inline-flex !important;
    top: 18% !important;
}

.edt-icon-wrk-cur-notallw {
    cursor: not-allowed !important;
}

//workflow-features.component.html

.srch-pb-none {
    border-bottom: none !important;
}

//workflow-groups.component.html

.nep-wrkflw-db-bg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.nep-wrk-dft-pr-dif {
    position: relative !important;
    display: inline-flex !important;
    top: 35% !important;
    width: 489px !important;
}

.nep-modal-workflw-db-bg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.yellow-info-pr-pb {
    padding-right: 8px !important;
    padding-bottom: 5px !important;
}

.wrkflow-nep-pr-dif-top-wid {
    position: relative !important;
    display: inline-flex !important;
    top: 35% !important;
    width: 489px !important;
}

//workflow-status.component.html

.srch-work-bb-no {
    border-bottom: none !important;
}

.table-width {
    width: 60px !important;
}

.txt-area-hght-ovrflw {
    height: auto !important;
    overflow: auto !important;
    color:#333333 !important;
}

.nepshw-db-bg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.nepshow-pr-dif-top {
    position: relative !important;
    display: inline-flex !important;
    top: 35% !important;
    width: 489px !important;
}

.nepmodal-pr-dtop-wid {
    position: relative !important;
    display: inline-flex !important;
    top: 35% !important;
    width: 489px !important;
}

//workflow-sub-features.component.html

.sub-fea-brdr-btm {
    border-bottom: none !important;
}

//workflow-trading-records-kpi.component.html

.wrkflw-outline {
    outline: none !important;
}

.workflw-fs-line-h {
    font-size: 14px !important;
    line-height: 24px !important;
}

.styl-clr-fs-lh {
    color: #E15400 !important;
    font-size: 12px !important;
    line-height: 14px !important;
    padding: 6px !important;
}

.rowflow-margin-tp {
    margin-top: 20px !important;
}

.wrkflw-trading-clr-fs-lh {
    color: #55565a !important;
    font-size: 14px !important;
    line-height: 20px !important;
}

.work-clr-black {
    color: #000000 !important;
}

.confir-sty-mt-fs-lh {
    margin-top: 20px !important;
    font-size: 14px !important;
    line-height: 24px !important;
}

.trading-line-h {
    line-height: 24px !important;
}

.edit-l-height {
    line-height: 24px !important;
}

.cus-style-db-backg {
    display: block !important;
    background: rgba(0, 0, 0, 0.25) !important;
}

.nep-trade-pr-dply-top {
    position: relative !important;
    display: inline-flex !important;
    top: 18% !important;
}

.ed-icon-cur-not {
    cursor: not-allowed !important;
}

.company-group-icon {
    top: 30px;
    position: absolute;
    right: 30px;
    cursor: pointer;
    color: #666666;
}

.pc-group-pop-up {
    width: 588px !important;
    max-width: 588px !important;

    .preference-content {
        height: auto !important;
    }

    .pc-group-menu {
        .preference-header {
            padding: 8px 16px !important;
            background: #FAFAFB 0% 0% no-repeat padding-box;
        }

        .menu-header {
            letter-spacing: 0px !important;
            color: #212121 !important;
            opacity: 1 !important;
            font-family: "" Helvetica Neue LT W05_55 Roman",Arial, Verdana, Tahoma,sans-serif", Arial, Verdana, Tahoma, sans-serif !important;
        }
    }

    .add-btn {
        width: 58px;
        padding: 5px 12px 5px 12px;
        background: #FFFFFF 0% 0% no-repeat padding-box;
        border: 1px solid #DEDFE0;
        border-radius: 0px 4px 4px 0px;
        cursor: pointer;
        letter-spacing: 0px;
        color: #4061C7 !important;
        opacity: 1;
        text-align: center;

        button {
            border: none;
            background: transparent;
            color: #4061C7;
        }
    }

    .isdisabled {
        opacity: 0.3;
    }

    .add-text {
        background: #FFFFFF 0% 0% no-repeat padding-box;
        border: 1px solid #DEDFE0;
        opacity: 1;
        width: calc(100% - 58px);
        padding: 5px 16px;
        border-right: none !important;
        border-radius: 4px 0px 0px 4px;

        input {
            width: 100%;
            border: none !important;
            padding-left: 0px !important;
            color: #4061C7 !important;
        }
    }


    .content-p {
        padding: 8px 16px !important;
    }

    .kpi-sec-search {
        background: #FFFFFF 0% 0% no-repeat padding-box;
        box-shadow: 0px 3px 6px #00000014;
        opacity: 1;
        border-bottom: 1px solid #DEDFE0;
    }

    .search-text-company {
        width: 100% !important;
        border: none !important;
    }

    .fasearchicon {
        top: 4px !important;
    }

    .drag-item {
        background: #FFFFFF 0% 0% no-repeat padding-box;
        border: 1px solid #DEDFE0;
        border-radius: 4px;
        opacity: 1;

        .drag-item-content {
            border-right: 1px solid #DEDFE0;
            padding: 5px 8px 5px 8px !important;
            height: 32px;
        }

        .icon-edit,
        .icon-delete {
            border-left: 1px solid #DEDFE0;
            height: 32px;
            padding: 4px 16px;
        }

        .group-content {
            padding: 6px 12px 7px 12px;
            height: 32px;

            label {
                text-align: left;
                letter-spacing: 0px;
                color: #212121;
                opacity: 1;
                width: 340px;
            }

            p-radiobutton {
                vertical-align: super !important;
            }

            .p-radiobutton {
                width: 16px;
                height: 16px;
            }

            .p-radiobutton .p-radiobutton-box.p-highlight {
                border-color: #4061C7;
                background: white;
            }

            .p-radiobutton .p-radiobutton-box {
                width: 16px;
                height: 16px;
            }

            .p-radiobutton .p-radiobutton-box .p-radiobutton-icon {
                width: 8px;
                height: 8px;
                transition-duration: 0.15s;
                background-color: #4061C7;
            }

            input[type="text"] {
                height: 18px;
                padding: 0px !important;
                display: inline-flex;
                width: 348px;
                position: absolute;
                margin-left: 8px;
                border-left: none;
                border-right: none;
                border-top: none;
                border-bottom: none;
                border-radius: 0px;
                font-size: 14px;
            }
        }
    }

    .company-group-list {
        width: 100%;
        max-width: 100%;
        display: block;
        max-height: 239px;
        overflow-y: auto;
    }

    .company-group-box {
        cursor: move;
    }


    .company-group-list.cdk-drop-list-dragging .company-group-box:not(.cdk-drag-placeholder) {
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }

    .company-group-custom-placeholder {
        background: #ccc;
        border: dotted 3px #999;
        min-height: 34px;
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }

    .company-grp-add-btn-b {
        border-top: 1px solid #DEDFE0;
        box-shadow: 0px 0px 12px #00000015;
    }
}

.cdk-drag-preview {
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
        0 8px 10px 1px rgba(0, 0, 0, 0.14),
        0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.internal-report-group-radio {
    .p-radiobutton .p-radiobutton-box.p-highlight {
        border-color: #4061C7 !important;
        background: white !important;
        width: 16px !important;
        height: 16px !important;
    }

    .p-radiobutton .p-radiobutton-box .p-radiobutton-icon {
        width: 8px !important;
        height: 8px !important;
        transition-duration: 0.15s !important;
        background-color: #4061C7 !important;
    }

    .p-radiobutton .p-radiobutton-box {
        width: 16px !important;
        height: 16px !important;
    }
}

.bg-tooltip-report.p-tooltip .p-tooltip-text {
    background: #F5F5F5 0% 0% no-repeat padding-box;
    letter-spacing: 0px;
    color: #55565A;
    opacity: 1;
    border: 1px solid #DEDFE0;
    border-radius: 4px !important;
}

.bg-tooltip-report .p-tooltip.p-tooltip-right .p-tooltip-arrow {
    border-right-color: #DEDFE0;
}

.bg-tooltip-report.p-tooltip.p-tooltip-left .p-tooltip-arrow {
    border-left-color: #DEDFE0;
}

.bg-tooltip-report .p-tooltip.p-tooltip-top .p-tooltip-arrow {
    border-top-color: #DEDFE0;
}

.bg-tooltip-report .p-tooltip.p-tooltip-bottom .p-tooltip-arrow {
    border-bottom-color: #DEDFE0;
}

.report-custom-dropdown {
    .p-inputtext {
        width: 85% !important;
    }
}

.investor-vintageDate {
    .p-autocomplete {
        padding-left: 1px;
        padding-right: 1px;
        padding-top: 1px !important;
        padding-bottom: 1px !important;
        border: 1px solid #C9C9D4 !important;
        border-radius: 4px;
    }

    .p-inputtext {
        border-bottom: none !important;
    }

    .p-button.p-button-icon-only {
        border-bottom: none !important;
    }
}

.pc-list-calender {
    .p-calendar {
        padding-top: 5px !important;
        input,button{
            border-color: #E6E6E6 !important;
        }
    }
}
.deals-list-calender {
    .p-calendar {
        input,button{
            border-color: #E6E6E6 !important;
        }
    }
}

.internal-tree-table {
    .child-table-border {
        table tbody>tr:last-child>td {
            border-bottom: none !important;
        }
    }

    .mat-custom-checkbox .mat-checkbox-frame {
        border: 1px solid #4061C7 !important;
    }

    .p-datatable .p-datatable-thead>tr>th {
        border: none !important;
        border-bottom: 1px solid #dee2e6 !important;
    }

    .p-datatable .p-datatable-tbody>tr>td:first-child,
    .p-datatable .p-datatable-thead>tr>th:first-child {
        border-left: none !important;
        overflow: visible !important;
    }

    table tbody>tr>td {
        border: none !important;
    }

    .p-datatable-tablewrapper>table,
    .p-datatable-wrapper>table {
        table-layout: auto !important;
        border-collapse: collapse !important;
    }
}

.finacials-beta-empty-state {
    .row {
        background: $nep-white !important;

    }
}

.previewEditorWrapper.ql-editor {

    ol,
    ul {
        padding-left: 0;
    }
}

.kpi-list-new {
    .p-dropdown {
        width: 200px;
        height: 31px;
        background: #FAFAFB 0% 0% no-repeat padding-box;
        border-bottom: 1px solid #DEDFE0;
        padding-left: 8px;

        .p-dropdown-label {
            padding-top: 4px;
            color: #000000;
            opacity: 1 !important;
            font-size: 14px;
        }

        .p-dropdown-panel {
            margin-top: 4px;
            box-shadow: 0 0 1px 1px rgb(0 0 0 / 2%), 0 6px 12px rgb(0 0 0 / 18%);
            transform-origin: 0 0;

            .p-dropdown-items {
                padding: 0px !important;

                .p-dropdown-item {
                    padding: 12px 16px;
                    padding-right: 8px !important;
                    text-align: left;
                    letter-spacing: 0px;
                    color: #55565A !important;
                    opacity: 1;

                    &:hover {
                        background-color: #F0F0F1 !important;
                        cursor: pointer;
                    }
                }

                .p-highlight {
                    background-color: #F0F0F1 !important;
                }
            }
        }
    }
}

.custom-kpilist-dropdown {
    .p-dropdown {
        width: 20rem;
        height: 2rem;
        border-bottom: 1px solid #DEDFE0;
        background: none !important;
    }
}

.highcharts-tooltip>span {
    background-color: rgba(255, 255, 255, 0.95);
    z-index: 9999 !important;
}

.page-section {
    .displayName {
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
        width: 80%;
        position: absolute;
        top: 6px;
    }
}

.toast-success {
    max-height: 80px !important;
    height: auto !important;
}

.deals-dropdown {
    .p-dropdown {
        width: 100% !important;
        border-right: none !important;
        border-left: none !important;
        border-top: none !important;
        border-radius: 0px !important;

        .p-dropdown-trigger {
            padding-top: 10px !important;
        }
    }
}

.valuation-f-table {
    .p-datatable-frozen-view {
        .p-datatable-scrollable-body {
            table {
                width: 600px !important;
            }
        }
    }

    .frozen-header {
        width: 300px !important;
    }

    .financial-header {
        width: 200px !important;
    }
}

.no-tree-structure {
    .p-treetable-toggler {
        margin-left: 0px !important;
    }
}

.form-control {
    height: calc(1.5em + 0.75rem + 2px);
}
.form-control.alternative{
    height: calc(0.5rem + 0.75rem + 2px);
}

p-table,
p-treetable {
    table {
        // border-collapse: inherit !important;
        // width: 100% !important;
        // table-layout: fixed;

        th,
        td {
            // max-width: 200px;
            border-color: #D5D5D5 !important;
        }
    }
}

.kpi-list-table {
    .p-datatable-header {
        padding: 0px !important;
        border: none !important;
    }
}

.virtual-row-h {
    height: 46px !important;
}

.fundlist-table {
    table tbody>tr>td {
        padding: 16px 20px;
        letter-spacing: 0px;
        color: #212121 !important;
        opacity: 1 !important;
        width: 100% !important;
    }

    table tbody>tr {
        border-bottom: 1px solid #DEDFE0 !important;

        &:hover {
            background: #EFF0F9 0% 0% no-repeat padding-box;
            cursor: pointer;
        }
    }

    table tbody>tr:last-child {
        border-bottom: none !important;
    }

    table tbody>tr>td>a {
        letter-spacing: 0px;
        color: #212121 !important;
        opacity: 1 !important;
    }
}

.fundlist-tree-table {
    table tbody>tr>td {
        padding-top: 18px !important;
        padding-bottom: 18px !important;
        width: 100% !important;
        padding-left: 14px !important;
    }
}

.cashflow-tbl tbody span.ui-column-title {
    display: none;
}

.removeTableBorder th {
    background-color: $nep-base-grey !important;
    border: none !important;
    color: $nep-text-grey;
    text-align: left;
}

.OperationalKPI {
    .p-datatable-frozen-view>.p-datatable-scrollable-body>table>.p-datatable-tbody>tr>td:first-child {
        padding-left: 16px !important;
        padding-right: 16px !important;
    }
}

.subfeature-tbl {
    .p-datatable {
        thead {
            .checkbox-header {
                padding-top: 11px !important;
                padding-bottom: 11px !important;
            }
        }
    }
}

.mdc-list-item__primary-text {
    display: inline-flex;
    overflow: unset !important;
}

.mdc-list-item__primary-text:hover {
    background: transparent !important;

    .mat-sidenav {
        .mdc-list-item__primary-text {
            display: flex;
            flex-direction: row;
            align-items: center;
            box-sizing: border-box;
            position: relative;
            height: 2.5rem !important;
        }

        .mat-expansion-panel {
            .mat-content {
                margin-right: 0px !important;
            }
        }
    }

    .form-control {
        height: calc(1.5em + 0.75rem + 2px);
    }
}

.mat-mdc-list-base .mat-mdc-list-item .mdc-list-item__content,
.mat-mdc-list-base .mat-list-option .mdc-list-item__content {
    display: flex;
    flex-direction: row;
    align-items: center;
    box-sizing: border-box;
    // padding: 0 16px;
    position: relative;
    height: inherit;
}

.modal-backdrop.show {
    z-index: 1050 !important;
}

.p-treetable .p-treetable-thead>tr>th.ValuationHeader:first-child {
    padding-left: 28px !important;
}

.mat-mdc-menu-content,
.mat-mdc-menu-content .mat-mdc-menu-item .mdc-list-item__primary-text {
    font-size: 14px !important;
}

.p-dropdown {
    .p-scroller-content {
        width: 100%;

        .p-dropdown-item {
            width: 100%;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;
        }
    }
}

.p-multiselect-items-wrapper {
    .p-scroller-content {
        width: 100% !important;
    }

    .p-multiselect-item {
        span {
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;
        }
    }
}

.p-autocomplete-panel {
    .p-scroller-content {
        width: 100% !important;
    }

    .p-autocomplete-items>.p-autocomplete-item {
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
    }
}

.search-text-company {
    color: #212121 !important;
}

.mdc-checkbox .mdc-checkbox__background {
    border-radius: 4px !important;
    border: 1px solid #cac9c7 !important;
    height: 20px !important;
    width: 20px !important;
    top: 0 !important;
    left: 0 !important;
}

.mdc-checkbox {
    padding: 0 !important;
    height: 20px !important;
    width: 20px !important;
}

.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,
.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,
.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background {
    background-color: #4061C7 !important;
    border: 1px solid #4061C7 !important;
}

.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark {
    color: #fff !important;
}

.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mdc-checkbox__ripple {
    background-color: unset !important;
}

.mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__ripple {
    background-color: unset !important;
}

.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark {
    border-color: #FFFFFF !important;
}

.p-icon-wrapper {
    svg {
        color: #75787b !important;
    }
}

.p-datepicker-calendar {
    th {
        border: none !important;
    }
}

.p-monthpicker-month {
    display: inline-flex;
}

.mat-mdc-tab-link .mdc-tab__ripple::before {
    background: transparent !important;
}

.fund-detail-button {
    .p-icon-wrapper svg {
        color: #FFFFFF !important;
    }

    .p-tieredmenu {
        width: 11.7rem !important;
    }
}


.p-checkbox .p-checkbox-box .p-checkbox-icon {
    color: #FFFFFF !important;
}

.p-scroller {
    min-height: 50px !important;
}

.kpi-node-text {
    letter-spacing: 0.17px !important;
    color: #212121 !important;
    font-weight: bold !important;

    .mdc-label {
        font-weight: bold !important;
    }
}

.mat-tree-node {
    label {
        margin-bottom: 0 !important;
    }
}

.master-kpi-table {
    .tr-table {
        .nep-tabs-tab {
            border-left-width: 0px !important;
            border-right-width: 0px !important;
            margin-right: 14px !important;
            padding: 8px 4px 8px 4px !important;
        }
    }
}

.table {
    border-collapse: collapse;
}

.custom-scroller-height {
    .p-scroller {
        height: 203px !important
    }
}

.company-financial-border {
    .p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody>tr>td:first-child {
        border-width: 0 1px 1px 0px !important;
    }

    .p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody>tr>td {
        border-width: 0 1px 1px 0px !important;
    }
}

.custom-border-hover .nep-button-link:focus,
.nep-button-link:hover {
    text-decoration: none;
}

.custom-button-hover .p-button:enabled:hover {
    color: #FFFFFF !important;
}

.kpi-prefence-filter {
    .p-dropdown .p-dropdown-trigger {
        padding-top: 4px !important
    }
}

.repo-table {
    .p-datatable.p-datatable-gridlines .p-datatable-thead>tr>th {
        border-width: 1px 1px 1px 0px !important;
    }

    .p-datatable.p-datatable-gridlines .p-datatable-thead>tr>th:last-child {
        border-width: 1px 1px 1px 0px !important;
    }
}

.custom-border-table-header {
    .p-datatable.p-datatable-gridlines .p-datatable-thead>tr>th {
        border-width: 1px 0px 1px 1px;
    }

    .p-datatable.p-datatable-gridlines .p-datatable-thead>tr>th:first-child {
        border-width: 1px 0px 1px 0px !important;
    }
}

.topholding-investor-table .p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody>tr>td {
    border-width: 0 0 1px 1px !important;
}

.investor-quarter {
    .querter-year-button-style .drop-toggle {
        padding-top: 4px !important;
        padding-bottom: 4px !important;
    }
}

.custom-freezoncolumn-border .p-treetable-frozen-view>.p-treetable-scrollable-body>table>.p-treetable-tbody>tr>td:last-child {
    border-right: 1px solid #DEDFE0 !important;
}

.mdc-text-field--filled:not(.mdc-text-field--disabled) {
    background-color: transparent !important;
}

.mdc-text-field--filled:not(.mdc-text-field--disabled):hover {
    background-color: transparent !important;
}

.mat-mdc-form-field-focus-overlay {
    background-color: transparent !important;
}

.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input {
    transition: opacity 0ms !important;
}

.custom-freezon-style {
    left: 84px !important;
}

.custom-reset-dataextraction button {
    background-color: rgb(240, 240, 240) !important;
    color: #000000 !important;
}

.internal-report-copy-multiselect {
    .p-multiselect-trigger {
        padding: 8px !important;
    }
}

.workflow-add-group textarea {
    color: #000000 !important;
}

.mat-mdc-chip {
    .mat-icon {
        padding: 0px 0px 0px 8px !important;
    }
}

app-donut-chart {
    svg {
        font-size: 12px !important;
    }

    .highcharts-no-data {
        display: none !important;
    }

    .highcharts-label {
        text {
            font-size: 11px !important;
        }
    }
}

.migration-frozen-table {

    .p-datatable-wrapper>.p-datatable-scrollable-table>.p-datatable-tbody>tr>td,
    .p-datatable-wrapper>.p-datatable-scrollable-table>.p-datatable-thead>tr>th {
        border: none;
        border-top: 1px solid #dee2e6 !important;
        border-right: 1px solid #dee2e6 !important;
    }

    .p-datatable-wrapper>.p-datatable-scrollable-table>.p-datatable-thead>tr>th {
        border-bottom: 1px solid #dee2e6 !important;
    }

    .p-datatable-wrapper>.p-datatable-scrollable-table>.p-datatable-tbody>tr:first-child>td {
        border-top: none !important;
    }
}

.consolidated-report-checkbox {
    .checkmark {
        left: 7px !important;
    }

    .p-datatable .p-datatable-thead>tr>th {
        border-top: none !important;
    }
}

.financials-v1,
.repo-v1 {
    .nep-tabs-line .nep-tabs-active:after {
        height: 3px !important;
        bottom: -1px !important;
    }
}

.tr-width-v1 {
    width: 100% !important;
}

.pageConfig-multiSelect {
    .p-multiselect {
        .p-multiselect-trigger {
            .p-icon-wrapper {
                padding-top: 6px !important;
            }
        }
    }

    .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
        background: #EFF0F9 !important;
    }

    .p-multiselect:not(.p-disabled).p-focus {
        border-color: #ced4da !important;
        box-shadow: none !important;
    }
}

.text-align-left {
    text-align: left !important;
}

.target-table-text {
    .form-control {
        height: calc(0.4em + 0.75rem + 2px) !important;
    }
}

.impliedev.migration-frozen-table {

    .p-datatable-wrapper>.p-datatable-scrollable-table>.p-datatable-tbody>tr>td:first-child,
    .p-datatable-wrapper>.p-datatable-scrollable-table>.p-datatable-thead>tr>th:first-child {
        border-left: 1px solid #dfdfdf;
    }

    .p-datatable-wrapper>.p-datatable-scrollable-table>.p-datatable-tbody>tr>td:last-child {
        border-right: none !important;
    }

    .p-datatable-wrapper>.p-datatable-scrollable-table>.p-datatable-thead>tr>td:last-child,
    .p-datatable-wrapper>.p-datatable-scrollable-table>.p-datatable-thead>tr>th:last-child {
        border-right: none !important;
    }

    .p-datatable-wrapper>.p-datatable-scrollable-table>.p-datatable-tbody>tr>td:first-child {
        border-left: none !important;
    }

    .p-datatable-wrapper>.p-datatable-scrollable-table>.p-datatable-thead>tr>td:first-child,
    .p-datatable-wrapper>.p-datatable-scrollable-table>.p-datatable-thead>tr>th:first-child {
        border-left: none !important;
    }
}


.toast-close-button {
    span {
        position: absolute;
    }
}

.data-analytics-confirm {
    .p-confirm-dialog {
        width: 490px !important;

        .p-dialog-content {
            padding: 20px !important;
        }
    }

    .p-dialog .p-dialog-header {
        padding: 0.75rem 1rem 0.75rem 2.5rem !important;
        border-bottom: 1px solid #e9ecef !important;
        background-color: #fafafb;
    }

    .p-dialog .p-dialog-footer button {
        padding: 0.375rem 1rem;
        width: auto;
        font-family: "Helvetica Neue LT W05_55 Roman", Arial, Verdana, Tahoma, sans-serif;
        border-radius: 3px !important;
    }

    .p-button-label {
        font-weight: 100 !important;
    }

    .p-dialog-footer {
        background-color: #fafafb;

        .cancel-btn {
            color: #4061C7;
            background-color: #fff;
            transition: background 0.15s ease-in-out;
        }

        .confirm-btn {
            background-color: #4061C7;
            color: #fff;
        }
    }
}

.data-analytics-dialog {
    .p-dialog {
        width: 38.25rem !important;

        .p-dialog-content {
            padding: 1.25rem 2.5rem !important;
        }
    }
}

.fixed-menu-repository.mat-mdc-menu-panel {
    height: 420px !important;
    min-width: 160px !important;
    max-width: 600px !important;
    overflow: none !important;
    overflow-y: none !important;
    overflow-x: none !important;
    -webkit-overflow-scrolling: none !important;
    max-height: none !important;
    border-radius: 4px;
    outline: 0;
}

.chart-dropdown {
    .p-dropdown-panel.p-component {
        max-width: 240px !important;
    }
}

.data-analytics-header {
    .p-selectbutton .p-button.p-highlight:hover {
        background: #FFFFFF !important;
    }

    .p-selectbutton .p-button.p-highlight {
        background: #FFFFFF !important;
    }

}

.repository-autocomplete {
    .p-autocomplete-item.p-highlight {
        color: #212529 !important;
        background: #e9ecef !important;
    }

    .p-autocomplete-item {
        padding: 0.5rem 0.5rem !important;
    }
}

.custom-data-analytics-tab {
    .nep-tabs-tab:first-child {
        padding-top: 0px !important;
        padding-bottom: 12px !important;
        border-left-width: 4px !important;
        border-right-width: 12px !important;
    }

    .nep-tabs-tab {
        padding-top: 0px !important;
        padding-bottom: 12px !important;
        border-left-width: 12px !important;
        border-right-width: 12px !important;
    }
}

.custom-ds-tab {
    .nep-tab-alignment {
        margin-top: 0px !important;
    }

    .nep-tabs-tab {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
        color: #666666;
        @extend .Body-R;
    }

    .nep-tabs-active {
        color: #4061C7 !important;
    }
}

.tab-shadow-home-bi {
    background: #FAFAFB 0% 0% no-repeat padding-box;
    opacity: 1;

    .mat-mdc-tab-nav-bar {
        background: $nep-white 0% 0% no-repeat padding-box;
    }
}

#DashboardView {
    margin-bottom: 20px !important;
}

.data-analytics-dropdown {
    .p-dropdown-items {
        padding: 8px !important;
    }

    .p-dropdown .p-dropdown-trigger {
        padding-top: 10px !important;
        padding-bottom: 0px !important;
    }

    .p-dropdown-label {
        padding-top: 6px !important;
        padding-bottom: 6px !important;
    }
}

.analytics-dropdown-pc {
    .p-dropdown {
        width: 100% !important;
    }
}

.track-record-header {
    font-size: 14px;
    font-weight: 400;
    left: 0;
    letter-spacing: 0;
    line-height: 20px;
    color: #000000;
}

.track-record-popup {
    border-bottom-left-radius: 4px !important;
    border-bottom-right-radius: 4px !important;
    padding: 0px 24px 24px 24px !important;

    .fa-search {
        right: 12px !important;
    }

    .card {
        border-radius: 0px !important;
    }

    .p-dialog-header {
        padding: 24px 24px 8px 24px !important;
    }

    .tr-popup-currency {
        color: #55565a;
        font-size: 12px;
        font-weight: 400;
        left: 0;
        letter-spacing: 0;
        line-height: normal;
    }

    .header-tr-bg {
        background: #FAFAFB !important;
    }

    .p-datatable .p-datatable-tbody>tr:last-child>td {
        border-bottom: 1px solid #dee2e6 !important;
    }

    .p-datatable .p-datatable-thead>tr>th:last-child,
    .p-datatable .p-datatable-tbody>tr>td:last-child {
        border-right: none !important;
    }
}


.kpi-list-new-group {
    .p-dropdown {
        width: 14.125rem;
        height: 31px;
        background: #FFFFFF 0% 0% no-repeat padding-box;
        border-bottom: 1px solid #DEDFE0;
        padding-left: 8px;
        word-wrap: break-word;
        white-space: normal;

        .p-dropdown-label {
            padding-top: 4px;
            color: #000000;
            opacity: 1 !important;
            font-size: 14px;
        }

        .p-dropdown-panel {
            margin-top: 4px;
            box-shadow: 0 0 1px 1px rgb(0 0 0 / 2%), 0 6px 12px rgb(0 0 0 / 18%);
            transform-origin: 0 0;

            .p-dropdown-items {
                padding: 0px !important;

                .p-dropdown-item {
                    padding: 8px 0px;
                    padding-right: 8px !important;
                    text-align: left;
                    letter-spacing: 0px;
                    color: #55565A !important;
                    opacity: 1;
                    margin-left: 0px;
                    padding-left: 32px;
                    word-wrap: break-word;
                    white-space: normal;

                    &:hover {
                        background-color: #F0F0F1 !important;
                        cursor: pointer;
                        margin-left: 0px;
                    }

                }

                .p-highlight {
                    background-color: #F0F0F1 !important;
                }
            }
        }
    }
}

.search-kpi-left {
    margin-left: -14px;
}

.btn-kpi-left {
    margin-right: 15px;
}

.document-calendar {
    .p-calendar {
        .p-button-icon-only {
            height: 30px !important;
        }
    }
}

.kpi-list-table-fs {
    .p-datatable .p-datatable-tbody > tr:last-child > td{
        text-align: left;
        border: 1px solid #dee2e6 !important; 
    }
    .p-scroller {
        min-height: calc(100vh - 187px) !important;
    }
    table {
        table-layout: fixed !important;
    }

    thead {
        th:first-child {
            width: calc(100% - 600px) !important;
        }

        th {
            width: 300px !important;
        }
    }
}


.kpi-list-new-popup {
    .p-dropdown {

        height: 31px;
        background: #FAFAFB 0% 0% no-repeat padding-box;
        border-bottom: 1px solid #DEDFE0;
        word-wrap: break-word;
        white-space: normal;

        .p-dropdown-label {
            padding-top: 4px;
            padding-left: 0px !important;
            color: #000000;
            opacity: 1 !important;
            font-size: 14px;
        }

        .p-dropdown-panel {
            margin-top: 4px;
            box-shadow: 0 0 1px 1px rgb(0 0 0 / 2%), 0 6px 12px rgb(0 0 0 / 18%);
            transform-origin: 0 0;

            .p-dropdown-items {
                padding: 0px !important;

                .p-dropdown-item {
                    padding: 8px 0px;
                    padding-right: 8px !important;
                    text-align: left;
                    letter-spacing: 0px;
                    color: #55565A !important;
                    opacity: 1;
                    margin-left: 0px;
                    padding-left: 32px;
                    word-wrap: break-word;
                    white-space: normal;

                    &:hover {
                        background-color: #F0F0F1 !important;
                        cursor: pointer;
                    }
                }

                .p-highlight {
                    background-color: #F0F0F1 !important;
                }
            }
        }
    }
}

.data-analytics-dialog {
    .dialog-header {
        font-size: 1.25rem;
        font-style: normal;
        font-weight: 400;
        line-height: 1.75rem;
        color: #000 !important;
    }

    .p-dialog-header {
        height: 3.25rem !important;
    }

    .popupcommon-labelpadding,
    label {
        font-size: 0.875rem;
        font-style: normal;
        font-weight: 400;
        line-height: 1.25rem;
        color: #6C6C7A !important;
    }

    .personal-dash-label {
        padding: 1.72rem 0px !important;

        &:last-child {
            padding-bottom: 0px !important;
        }
    }

    .custom-nep-input {
        textarea {
            height: auto !important;
        }

        .nep-input-textarea {
            border-bottom: 1px solid #C9C9D4 !important;
        }
    }
}

.kpi-new-group {
    .p-dropdown-panel {
        width: 14.125rem !important;

        .p-dropdown-items {
            li {
                width: 100% !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                white-space: nowrap !important;
            }
        }
    }
}

.kpi-new-group-popup {
    .p-dropdown-panel {
        .p-dropdown-items {
            li {
                width: 100% !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                white-space: nowrap !important;
            }
        }
    }
}
.custom-calendar-width {
    div>p-calendar>span>div {
        top: 32px !important;
        width: auto !important;
        border-top: none !important;
    }
}
.repo-table {
    width: 12.5rem;
}
.custom-audit-model {
    .nep-card-header {
        margin-bottom: 0px !important;
        border-bottom: 1px solid #DEDFE0 !important;
    }
    .nep-card-body {
        padding: 1.5rem 2.5rem !important;
    }
    .nep-card-footer {
        border-top: 1px solid #DEDFE0 !important;
    }

    .label-code {
        font-size: 14px !important;
        font-weight: 600 !important;
        color: #2B2B33 !important;
        line-height: 1.25rem !important;
    }

    .label-code-value {
        color: #2B2B33 !important;
        line-height: 1.25rem !important;
    }

    .view-audit-mt-fs {
        color: #2B2B23 !important;
    }

    .aduit-pc-clr {
        color: #2B2B33 !important;
    }
}

p-autocomplete {
    .p-overlay {
        width: 100% !important;
    }
}
.data-analytics-dots {
    background: $nep-white !important;
    border-left: 1px solid #dee2e6 !important;
    border-bottom: 1px solid #dee2e6 !important;

    .fasearchicon {
        right: auto !important;
        top: 9px !important;
        font-size: 15px !important;
        color: #75787B !important;
    }


    .delete-icon {
        margin-right: 1rem !important;
        padding-top: 9px !important;
        cursor: pointer !important;
    }

    .col-divider {
        border-right: 1px solid #DEDFE0;
    }

    .data-analytics-col-divider {
        margin-top: 0.75rem !important;
        margin-left: 2rem !important;
    }
}

.data-analytics-header-left{
    width: calc(100% - 99px) !important;
}
.data-analytics-header-right{
    width:99px !important;
    text-align: center !important;
}
p-autocomplete
{
.p-overlay
{
    width: 100% !important;
}
}
.file-error-lst-table
{
     table tbody > tr:hover {
        background: white !important;
    }
}
.file-error-table
{
    .toggler-row{
            padding: 0px !important;
    }
}
.error-list-popup
{
    padding-left: 48px !important;
    .error-code
    {
        color:red !important;
    }
}
.p-dropdown-panel .p-dropdown-header .p-dropdown-filter {
    padding-right: 1.75rem !important;
}
.p-multiselect-panel .p-multiselect-header .p-multiselect-filter-container .p-inputtext{
    padding-right: 1.75rem !important;
}
.InputText {
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: 1px solid $gray-600 !important;
    width: 100% !important;
    color: $nep-text-grey !important;
    cursor: text !important;
    border-bottom: none !important;
  }
  .kpi-list-formula-builder {
    .p-dropdown {
        max-width: 100% !important;
        width: 100%;
        height: 31px;
        background: #FAFAFB 0% 0% no-repeat padding-box;
        border-bottom: 1px solid #DEDFE0;
        padding-left: 8px;
        word-wrap: break-word;
        white-space: normal;

        .p-dropdown-label {
            padding-top: 4px;
            color: #000000;
            opacity: 1 !important;
            font-size: 14px;
        }

        .p-dropdown-panel {
            margin-top: 4px;
            box-shadow: 0 0 1px 1px rgb(0 0 0 / 2%), 0 6px 12px rgb(0 0 0 / 18%);
            transform-origin: 0 0;

            .p-dropdown-items {
                padding: 0px !important;

                .p-dropdown-item {
                    padding: 8px 0px;
                    padding-right: 8px !important;
                    text-align: left;
                    letter-spacing: 0px;
                    color: #55565A !important;
                    opacity: 1;
                    margin-left: 0px;
                    padding-left: 32px;
                    word-wrap: break-word;
                    white-space: normal;

                    &:hover {
                        background-color: #F0F0F1 !important;
                        cursor: pointer;
                        margin-left: 0px;
                    }

                }

                .p-highlight {
                    background-color: #F0F0F1 !important;
                }
            }
        }
    }
}
.audit-log-table-header{
    border-width: 1px 0px 1px 1px !important;
}
.highcharts-y-axis-font-size text {
    font-size: 14px !important;
}
.search-users
{
    border-right: 1px solid #DEDFE0 !important;
}
.analytics-filter-multi-select-items {
    .ui-multiselect-label {
        display: flex;
        align-items: center;
    }
    .p-multiselect .p-multiselect-label-container{
        padding-right: 3rem !important;
    }
    .p-multiselect .p-multiselect-clear-icon {
        position: absolute;
        margin-top: -16px !important;
        cursor: pointer;
        right: 30px !important;
    }
}
    .nep-all-upload-file{
        position: relative !important;
        display: inline-flex !important;
        width: 784px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
    }
    .all-bulk-upload{
        .mat-mdc-chip .mat-icon {
            padding: 2px 0px 0px 4px !important;
        }
        .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{
            width: 100% !important;
            padding-left: 0px !important;
        }
        .mat-mdc-chip-action-label{
            width: 100% !important;
        }
        .mat-mdc-chip.mat-mdc-standard-chip {
            --mdc-chip-container-height: 28px;
        }
        .mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) {
            background-color: #F2F2F2 !important;
        }
        .mdc-evolution-chip__action--primary{
            max-width: 100% !important;
        }
        .mat-mdc-standard-chip .mdc-evolution-chip__action--primary {
            padding-left: 4px;
            padding-right: 4px;
        }
    }
    .left-align{
        float: left !important;
        text-align: left;
      }
.custom-esg-pop
{
    top:8px;
}
.kpi-mapping-truncate{
.mdc-form-field{
    max-width: 100% !important;
}
label{
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}
}
.esg-static-info {       
    .nep-card-body {
        padding: 0 0 !important;
    }
}
.audit-table-pc,
.audit-table-esg
{
    th:last-child,td:last-child {
        border-right: none !important;
    }
    .p-datatable .p-datatable-scrollable-view .p-datatable-thead > tr > th:last-child,
    .p-datatable .p-datatable-scrollable-view .p-datatable-tbody > tr > td:last-child {
        border-right: 1px solid $nep-nav-tab-border-color !important;
    }
    tr td:nth-child(6), tr th:nth-child(6) {
      width: 120px !important;
    }
    .p-datatable{
        bottom: 1px !important;
    }
}

.mt-20{
    margin-top: 20%;
}

.wd-100{
    width:100%;
}
#loaderContainer{
    background: rgba(0, 0, 0, 0.25) !important;
    .loader-body
    {
        position: relative;  
        top: 35%;
    }
    .lds-ellipsis {
        display: inline-block;
        position: relative;
        width: 80px;
    }
    
    .lds-ellipsis div {
        position: absolute;
        top: 33px;
        width: 13px;
        height: 13px;
        border-radius: 50%;
        background: #4061C7;
        animation-timing-function: cubic-bezier(0, 1, 1, 0);
    }
    
    .lds-ellipsis div:nth-child(1) {
        left: 8px;
        animation: lds-ellipsis1 0.6s infinite;
    }
    
    .lds-ellipsis div:nth-child(2) {
        left: 8px;
        animation: lds-ellipsis2 0.6s infinite;
    }
    
    .lds-ellipsis div:nth-child(3) {
        left: 32px;
        animation: lds-ellipsis2 0.6s infinite;
    }
    
    .lds-ellipsis div:nth-child(4) {
        left: 56px;
        animation: lds-ellipsis3 0.6s infinite;
    }
    
    @keyframes lds-ellipsis1 {
        0% {
            transform: scale(0);
        }
        100% {
            transform: scale(1);
        }
    }
    
    @keyframes lds-ellipsis3 {
        0% {
            transform: scale(1);
        }
        100% {
            transform: scale(0);
        }
    }
    
    @keyframes lds-ellipsis2 {
        0% {
            transform: translate(0, 0);
        }
        100% {
            transform: translate(24px, 0);
        }
    }
    
    .nep-spin-ring {
        margin: auto;
        border-style: solid;
        border-color: #f5f5f7;
        -webkit-animation: frames 1s infinite linear;
        animation: frames 1s infinite linear;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        border-width: 5.4px;
        border-top-color: #4061C7
    }
    
    @keyframes frames {
        0% {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
        }
        100% {
            -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
        }
    }
}
.custom-lp-fund-report {
    .p-inputtext {
        .custom-label {
            width: 150px;
        }
    }
    .p-dropdown-panel {
        &.p-component.ng-star-inserted {
            width: 240px;
        }
    }
    
}
.workflow-toggle {
    .custom-font{
        font-size: 14px;
    }
}
.workflow-btn-font{
    .workflow-toggle{
        button{
            font-size: 14px !important;
        }
    }
}
.kpi-list-zero
{
    .zeroStateKpi{
        border: none !important;
    }
}
.report-download-dropdown{
    .p-overlay{
        width: 100% !important;
    }
}
.cab-table-tab 
{
    .mat-mdc-tab-link-container
    {
        background: transparent !important;
        margin-left: 1rem !important;
        margin-right: 1rem !important;
        .mdc-tab--active
        {
            background: $nep-white !important;
        }
        &::before
        {
            margin-top: -0.5px;
        }
    }
}
.cap-table-menu
{
    .label-align
    {
        padding-top: 0.75rem !important;
    }
    .p-dropdown
    {
        width:12.5rem !important ;
    }
}
.cap-table-dropdown {
    width: 12.5rem !important;
    min-width: 12.5rem !important;
}

.cap-table-menu.mat-mdc-menu-panel {
    height: 11.875rem !important;
    width: 14.25rem !important;
    margin-top: 0.375rem !important;
}
.cap-table-section, .valuation-Summary{
    .panel
    {
        border: 1px solid transparent !important;
    }
}
.header-icon-wrapper {
    display: flex;
    align-items: center;
  }
  
  .header-text {
    flex-grow: 1;
  }

  .border-bottom{
    border-bottom: 1px solid #dee2e6 !important;
  }
.input-with-icon {
    position: relative;
}
.input-with-icon input {
    padding-right: 1.75rem !important;
}
.input-with-icon .search-icon {
    position: absolute !important;
    padding-left: 0.5rem !important;
}
.wd-98{
    max-width:98%;
}
.portfolio-company-list-table 
{
    .k-master-row {
        text-align: left;
        font-size: 14px;
        letter-spacing: 0px;
        color: $nep-dark-black;
        opacity: 1;
        .company-buttons {
            display: none;
            margin-bottom: -5px;
            margin-top: -4px;
        }
        .items {
            padding: 20px 12px;
            .TextTruncate {
                overflow: inherit !important;
            }
        }
        .sectorHeadQuarter {
            background: $nep-white 0% 0% no-repeat padding-box;
            border: 1px solid $nep-divider;
            border-radius: 4px;
            opacity: 1;
            padding: 4px 12px;
        }
        &:hover {
            background: $nep-row-active 0% 0% no-repeat padding-box !important;
            border: 1px solid $nep-divider;
            .company-buttons {
                display: block;
            }
        }
    }
}
.label-text
{
    label{
        &:after {
            padding-left: 2px !important;
            content: "*";
            color: red !important;
        
        }
    }
}
.consolidated-grid {
    .mat-mdc-checkbox {
        padding-left: 2px;
    }
    .checkmark {
        top: -10px !important;
        left: 2px !important;
    }
}
label,.label-color{
    color: #666666 !important;
}
.location-form{
    .Caption-M{
        color: #666666;
    }
}
.custom-to-date{
    kendo-popup{
        top:0px !important;
    }
}
.nep-tabs-active,
.nep-tabs-tab {
    @extend .Body-R;
}
.nep-tabs-tab {
    color: #666666;
}
.repo-input-height{
    height: 35px !important;
}
.fund-header-tbl {
    th {
        @extend .Body-R;
    }
}
.custom-home-sec-tab{
    .mat-mdc-tab-link-container::before{
        top:97% !important;
    }
}
.p-label-padding.filter-label,
.p-label-padding.custom-label {
    @extend .Caption-M;
    color: #666666;
}

.container {
    display: block;
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    color: #1A1A1A !important;
    font-size: 14px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  .container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
  }
  
  .checkmark {
    position: absolute;
    top: 0.125rem;
    left: 0;
    height: 1rem;
    width: 1rem;
    background-color: #ffffff;
    border: 1px solid #4061C7 !important;
    border-radius: 50%;
  }
  .checkmark:hover {
    position: absolute;
    top: 0.125rem;
    left: 0;
    height: 1rem;
    width: 1rem;
    background-color: #ffffff;
    border: 1px solid #152B7A !important;
    border-radius: 50%;
  }
  
  .container:hover input ~ .checkmark {
    background-color: #EBF3FF;
  }
  
  .container input:checked ~ .checkmark {
    background-color: #ffffff;
  }
  
  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }
  
  .container input:checked ~ .checkmark:after {
    display: block;
  }
  
  .container .checkmark:after {
    top: 0.088rem;
    left: 0.1rem;
    width: 0.688rem;
    height: 0.688rem;
    background: #4061C7;
    border-radius: 50%;
  }
  .container .checkmark:hover:after {
    top: 0.088rem;
    left: 0.1rem;
    width: 0.688rem;
    height: 0.688rem;
    background: transparent;
  }
.padding-val-report-cal{
    padding: 0.375rem 0.75rem !important;
}
.filter-collapse-icon
{
    margin-top: -2px;
    color: #6c757d;;
}
.api-spot-rate-circle{
    width: 18px;
    height: 16px;
    cursor: pointer;
    background-color: #3949AB;
    color: #FFFFFF;
    border-radius: 50%;
    text-align: center;
    right: 10px;
    top: 0px;
    position: absolute;
}
input[readonly] {
    background-color: #F2F2F2 !important;
    border-color: #CCCCCC !important;
}

.static-info-save {
    width: calc(100% - 280px);
    max-width: calc(100% - 280px);
    white-space: normal;
    overflow-wrap: normal;
    word-break: normal;
    padding: 12px 0px 5px 25px;
    color: #50505C;
}
.static-info-modification .config-note div{
    padding: 15px 0px !important;
    max-width: 100% !important;
    width: 100% !important;
}
p-calendar{
    input[readonly]
    {
        background: transparent !important;
    }
}
.truncate {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    max-width: 250px;
}
.custom-confirm-model
{
    .nep-card{
        border:none !important;
        border-radius: 8px;
        .nep-card-body{
            padding: 16px 24px;
        }
    }
    .nep-card > div:first-child{
        border-bottom: none !important;
    }
    .nep-card-header{
        border-top-right-radius: 8px;
        border-top-left-radius: 8px;
    }
}
.growth-report-pop-up{
    margin-top: 2px !important;
    width: 300px;
    max-width: 300px !important;
    box-shadow: 0px 0px 8px 0px #0000001F !important;
    .pop-up-section{
        padding: 0.5rem 0.75rem 0.5rem 1rem;
        .sub-section{
            padding: 8px 0px ;
            @extend .Body-R;
            color: #333333;
            img{
                vertical-align: text-bottom;
            }
            label {
                color: #666666;
                padding-bottom: 0.25rem;
            }
        
            .req-label {
                padding-bottom: 0.25rem;
                &:after {
                    content: "*";
                    color: red;
                    padding: 4px;
                }
            }
        }
        .v-line{
            width: calc(100% + 28px);
            margin-right: -16px;
            margin-left: -16px;
            border-bottom: 1px solid #F2F2F2;
        }
    }
}
.popup-split-button-custom-primary-bg{
    position: absolute !important;
    right: -140px !important;
}
#nepGrowthTab {
    .nep-tabs-tab {
        border-left-width: 0px !important;
        border-right-width: 0px !important;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
}
.kpi-list{
    .k-input-inner{
        padding: 0 !important;
    }
}
.auto-adjust{
    white-space:normal !important;
    word-wrap:break-word !important;
    height:auto !important;
}
#esgStaticInfoUpdateCancel{
    .nep-card-footer{
        border-top : 1px solid #E6E6E6 !important
    }
}
.feature-tree-structure {
    .p-treetable-toggler {
        height: 32px !important;
    }
}
.content-kpi-section
{
    .ng-invalid.k-multiselect-custom {
        border-bottom-color: #E6E6E6!important; 
      }
}
.custom-report-label{
    .ng-untouched
    {
        border-bottom-color: #E6E6E6 !important;
    }
}
.custom-r-label{
    input[readonly]{
        background:transparent !important;
    }
}
.k-data-order-custom-popup {
    width: 240px !important;
    max-height: 300px;
    overflow-y: scroll;
}

.k-order-button-focus {
    .k-button-solid-base:focus,
    .k-button-solid-base.k-focus {
        box-shadow: none !important;
    }
}
.k-order-button{
    .k-button-text{
        width: 100% !important;
    }
}

.methodology-drp span.k-input-inner .k-input-value-text::before {
    content: none !important;
}
.pc-comment quill-editor .ql-container.ql-snow{
    border:none !important;
  }

  .pc-comment quill-editor .ql-toolbar.ql-snow{
    border-left: none !important;
    border-right: none !important;
  }
  .k-custom-group-parent-child {
    .k-list-group-item {
        border-width: 0 !important;
    }
    .k-list-md .k-list-group-item {
        padding-block: 16px !important;
        padding-inline: 12px !important;
    }
    .k-list-md .k-list-item,
    .k-list-md .k-list-optionlabel {
        padding: 16px 32px !important;
    }
}
.vs-filter-menu .k-input-inner{
    padding-left: 0px !important;
}
.border-top .valuation-summary-table kendo-grid-list .k-grid-content table tbody tr:first-child td:first-child{
  background: #F5F9FF !important;
}  .border-top .valuation-summary-table kendo-grid-list .k-grid-content table tbody tr:first-child td:first-child:hover{
  background: #F5F9FF !important;
}
.nav-bar-top
{
  height:60px;
  box-shadow:$shadow-short;
  background:$Neutral-Gray-00 !important;
  padding-right:1.5rem;
}
.fixed-menu {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 999;
}
.border-top .valuation-summary-table kendo-grid-list .k-grid-content table tbody tr:last-child td{
    border-bottom: 0px;
}
.custom-quill-editor {
    .ql-container {
        min-height: 100px !important;
        height: auto !important;
        overflow-y: hidden !important;
    }
}

.custom-footnote {
    display: flex;
    height: 46px;
    align-items: center;
    justify-content: space-between;
}
.data-ingestion-sidebar{
    width: 416px !important;
}

.user-info-grid {
  .k-grid-content .k-table-row:last-child>td,
  .k-grid-content .k-table-row:last-child>.k-table-td,
  .k-grid-content-locked .k-table-row:last-child>td,
  .k-grid-content-locked .k-table-row:last-child>.k-table-td {
    border-bottom: 1px solid $nep-nav-tab-border-color !important; 
  }
  // Ensure Kendo Grid row and cell height is 43px
 .k-grid-table tr,
 .k-grid-table td,
 .k-grid-table th {
    height: $row-height;
    min-height: $row-height;
    max-height: $row-height;
    line-height: $row-height;
    padding-top: 0;
    padding-bottom: 0;
  }
}
.extraction-main-checkbox {
    .custom-kpi-header-style {
        @extend .Body-M;
        color:$Neutral-Gray-90;
        padding-top:0.5rem !important;
    }
    label{
        margin-bottom:0px !important;
    }
}

.content-secondary{
    color: $gray-color-66 !important;
}

.delete-modal-button-secondary{
    color: $gray-color-66 !important;
    border-color: $gray-color-66 !important;
}

.mr-13{
    margin-right: 13px !important;
}

.dashboard-tracker-table{
    .k-grid-aria-root{
    border-radius: 4px !important;
  }
}

.h-100{
    height: 100% !important;
}

.app-button-primary {
  border: 1px solid $Primary-78 !important;
  background-color: $Primary-78 !important;
  color: $Neutral-Gray-00 !important;
    .button-icon-primary {
        color: $Neutral-Gray-00 !important;
    }
    span{
        display: flex !important;
        color: $Neutral-Gray-00 !important;
    }
}

.app-button-primary:hover {
  background-color: $Primary-90 !important;
}

.app-button-primary:active {
  border: 1px solid $Primary-100 !important;
  background-color: $Primary-100 !important;
}

.app-button-primary:focus {
    box-shadow: 0 0 0px 2px #30374B !important;
}

.app-button-secondary {
  border: 1px solid $Primary-78 !important;
  color: $Primary-78 !important;
    .button-icon-secondary {
        color: $Primary-78 !important;
    }
    span{
        display: flex !important;
        color: $Primary-78 !important;
    }
}

.app-button-secondary:hover {
  background-color: $nep-light-h-bg !important;
}

.app-button-secondary:active {
  border: 1px solid $Primary-90 !important;
  color: $Primary-90 !important;;
  background-color: $nep-light-h-bg !important;
    .button-icon-secondary {
        color: $Primary-90 !important;
    }
}

.app-button-secondary:focus {
    box-shadow: 0 0 0px 2px #30374B !important;
}
  .dark-title label {
      color: #1A1A1A !important;
  }

#dashboard-configuration-container{
    .dashboard-tracker-table td{
        padding: 8px 16px !important;

        .k-custom-solid-dropdown.k-dropdown-height-32.k-rounded-md.k-input-solid.k-input-md.k-combobox.k-input {
            border-radius: 0px;
            border: none;
            border-bottom: 1px solid $Neutral-Gray-10;
        }
    }
   
    .dashboard-tracker-table .k-grid-header{
        padding: 0px !important;
    }
    .dashboard-tracker-table .k-grid-content{
        -ms-overflow-style: none !important;
        scrollbar-width: none !important;
        transition: overflow-y 0.2s;
    }    
}

.manage-tracker-records-panel{
    .k-expander-header {
        background-color: #ffffff !important;
        border: 0px !important;
    }
}
.fixed-fxrates-menu.mat-mdc-menu-panel {
    min-width: 100% !important;
    max-width: 100% !important;
    overflow: none !important;
    overflow-y: none !important;
    overflow-x: none !important;
    -webkit-overflow-scrolling: none !important;
    max-height: none !important;
    border-radius: 4px;
    outline: 0;
}
.fixed-fxrates-menu{
    margin-top:0.75rem !important;
}
.example-input{
    border: 1px solid $Neutral-Gray-10 !important ;
    border-radius: $Radius-4;
    padding: 6px 16px !important;
    width: calc(100% - 2rem);
    height: 36px !important;
    color:$Neutral-Gray-80 !important;
    background: $white-color;
    overflow: auto;
    &:focus,&:active,&:focus-visible{
      border: 1px solid $primary-color-78 !important;
    }
  }
  .manage-pages{
.k-window-titlebar-actions{
  display: none !important;
}
} 
.mat-checkbox-large,.mat-checkbox-medium{
    .mdc-checkbox__background{
        width:1rem !important;
        height:1rem !important;
    }
    .mdc-label{
        margin-bottom:0px !important;
        color:$Neutral-Gray-90 !important;
    }
    .mat-mdc-checkbox-touch-target{
        width:24px !important;
        height:24px !important
    }
    .mdc-checkbox__checkmark{
        width: 10px !important;
        height: 12px !important; 
        left:2px !important;
    }
    .mdc-checkbox__mixedmark{
        width: 8px !important;
    }
}
.flat-data-table-wrapper .custom-kendo-cap-table-grid .k-grid-content.k-virtual-content {
  overflow-y: hidden !important;
}
.flat-data-table-wrapper .custom-kendo-cap-table-grid .k-grid-header,
.flat-data-table-wrapper .custom-kendo-cap-table-grid .k-grid-header.ng-star-inserted {
  padding: 0px !important;
}