$border-color-dark: 1px solid #e6e6e6;
$background: #f9f9f9;
$border-color-light:1px solid #ccc;
$background-header:#fafafa;
$required: #de3139;

.selected-slide {
  border: 2px solid #007bff !important;
  background: #eaf4ff !important;
  box-shadow: 0 0 8px #007bff33;
}

.resizable-drop-zone {
  border: 2px dashed #28a745;
  background: #f8f9fa;

  &:hover {
    border-color: #20c997;
    background: #e9f7ef;

    .drop-zone-resize-handle {
      display: block;
    }
  }
}

.drop-zone-resize-handle {
  display: none;
  background: #28a745;
  border: 2px solid #fff;
  border-radius: 2px;

  &:hover {
    background: #218838;
  }
}

.drop-zone ,
.drop-zone-footer{
  border: 2px dashed #ccc;

  &.resizable-drop-zone {
    border-color: #28a745;
    color: #666;
    background: #f8f9fa;
  }
}

label {
  font-weight: 600;
  margin-bottom: 5px;
}

.required {
  color: $required !important;
}

.save-btn,
.preview-btn,
.cancel-btn {
  align-self: flex-end;
  min-width: 120px;
}
.apply-btn{
align-self: center !important;
margin-top: 5px !important;
align-self: flex-end;
}



//final css
.automated-newsletter {
  padding: 20px;

  .controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
    border: $border-color-dark;
    background: $background-header;
    border-radius: 8px;

    label {
      margin-right: 5px;
    }

    .apply-btn {
      margin-left: auto;
      padding: 5px 10px;
    }
  }

  .main-layout {
    display: flex;
    gap: 20px;

    .line-items {
      width: 20%;
      background: $background-header;
      border:$border-color-dark;
      border-radius: 5px;
    }

    .newsletter-template {
      width: 60%;
      background: $background-header;    
      border: $border-color-dark;
      border-radius: 5px;

      .template-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
      }
      .template-header-border{
        border-bottom: $border-color-dark;
        border-radius: 5px;
        padding: 0;
      }

      .template-content {
        margin-top: 15px;
        padding: 15px;

        .section {
          background: $background;
          padding: 10px;
          border-radius: 4px;
        }
      }
    }
  }
  .list-container {
  margin: auto;
  background: $background;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 5px;
  background: white;
  border: $border-color-dark;
  border-radius: 5px;
  cursor: move;
}

.handle {
  margin-right: 10px;
  font-size: 18px;
}
.no-content-section{
  min-height: 80vh;
    justify-content: center;
    align-items: center;
    display: flex;
    flex-direction: column;
}



}
.automated-newsletter {
  .controls {
    .error-image{
          margin-bottom: 4px;
    }
    .apply-btn{
        width:100%;
            max-width: 80%;
    }
    .apply-btn-container{
          min-height: 83px;
          align-items: center;
          align-content: center;
    }
  }
}
/* Media query */
@media (min-width: 992px) {
    .controls {
    .apply-btn{
        max-width:50%;
    }
    .apply-btn-container {
    padding: 0 15px;
    }
    }
}
@media (max-width: 576px) {
  .controls {
    .apply-btn{
        max-width:50%;
    }
    .apply-btn-container {
    padding: 0 15px;
  }
  }
  
} 