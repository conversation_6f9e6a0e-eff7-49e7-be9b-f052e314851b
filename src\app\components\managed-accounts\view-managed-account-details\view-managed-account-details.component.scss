@import '../shared/shared-tab-styles.scss';

// Main component specific styles - these are unique to the main component
// Responsive styles are now in shared-tab-styles.scss



// Table styles are now in shared-tab-styles.scss
// Button and utility styles are now in shared-tab-styles.scss

:host ::ng-deep {
  .k-tabstrip {
    width: 100%;
    
    
      
      .k-item {
        color: #666666;
        
        &.k-active {
          color: #4061C7;
          border-bottom: 2px solid #4061C7;
        }
      }
    
  }
}

:host ::ng-deep .k-tabstrip-top > .k-content, 
:host ::ng-deep .k-tabstrip-top > .k-tabstrip-content {
    padding: 0px;
    border: none;
}

// Utility styles are now in shared-tab-styles.scss